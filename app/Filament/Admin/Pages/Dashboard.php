<?php

namespace App\Filament\Admin\Pages;

use App\Filament\Admin\Widgets;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use Filament\Panel;

class Dashboard extends \Filament\Pages\Dashboard
{
    use HasFiltersAction;

    protected function getHeaderActions(): array
    {
        return [
            // FilterAction::make()
            //     ->form([
            //         DatePicker::make('startDate'),
            //         DatePicker::make('endDate'),
            //         // ...
            //     ]),
        ];
    }

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->widgets([
                Widgets\StatsOverview::class,
            ]);
    }
}
