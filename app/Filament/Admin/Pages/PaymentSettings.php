<?php

namespace App\Filament\Admin\Pages;

use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Log;
use Mollie\Laravel\Facades\Mollie;

class PaymentSettings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationLabel = 'Payment Settings';

    protected static ?int $navigationSort = 21; // Settings is 20, so this will appear right after

    protected static ?string $navigationGroup = 'Instellingen';

    protected static ?string $title = 'Payment Settings';

    protected static bool $shouldRegisterNavigation = true;

    protected static string $view = 'filament.admin.pages.payment-settings';

    protected static ?string $slug = 'payment-settings';

    public ?array $data = [];

    public function mount(): void
    {
        $tenantData = tenant()->getOriginal('data');
        $this->form->fill($tenantData);
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                Section::make('Payment Methods')
                    ->description('Configure payment methods for your reservation form')
                    ->schema([
                        Toggle::make('payment.enable_payment_method_selection')
                            ->label('Enable payment method selection in form')
                            ->helperText('If disabled, customers won\'t need to choose a payment method')
                            ->onIcon('heroicon-m-check')
                            ->offIcon('heroicon-m-x-mark'),

                        Toggle::make('payment.enable_local_payment')
                            ->label('Enable local payment (in person)')
                            ->helperText('Allow customers to pay in person')
                            ->onIcon('heroicon-m-check')
                            ->offIcon('heroicon-m-x-mark')
                            ->reactive(),

                        // CheckboxList::make('payment.local_payment_methods')
                        //     ->label('Local payment methods')
                        //     ->options([
                        //         'cash' => 'Cash',
                        //         'pin' => 'Pinnen',
                        //         'creditcard' => 'Creditcard',
                        //         'tikkie' => 'Tikkie',
                        //         'applepay' => 'Apple Pay',
                        //     ])
                        //     ->columns(2)
                        //     ->visible(fn (callable $get) => $get('payment.enable_local_payment')),

                        Toggle::make('payment.enable_online_payment')
                            ->label('Enable online payment')
                            ->helperText('Allow customers to pay online via Mollie')
                            ->onIcon('heroicon-m-check')
                            ->offIcon('heroicon-m-x-mark')
                            ->reactive(),

                        TextInput::make('mollie.key')
                            ->label('Mollie API Key')
                            ->helperText('Enter your Mollie API key here')
                            ->required(fn (callable $get) => $get('payment.enable_online_payment'))
                            ->visible(fn (callable $get) => $get('payment.enable_online_payment')),

                        Select::make('payment.online_payment_limit_type')
                            ->label('Only allow online payment after')
                            ->options([
                                'distance' => 'Distance (km)',
                                'price' => 'Price (€)',
                                'none' => 'No limit',
                            ])
                            ->default('none')
                            ->visible(fn (callable $get) => $get('payment.enable_online_payment'))
                            ->reactive(),

                        TextInput::make('payment.online_payment_limit_value')
                            ->label('Limit value')
                            ->numeric()
                            ->minValue(0)
                            ->visible(fn (callable $get) =>
                                $get('payment.enable_online_payment') &&
                                in_array($get('payment.online_payment_limit_type'), ['distance', 'price'])
                            ),
                    ]),
            ]);
    }

    public function save(): void
    {
        $formData = $this->form->getState();

        // Validate Mollie API key if online payment is enabled
        if (isset($formData['payment']['enable_online_payment']) &&
            $formData['payment']['enable_online_payment'] &&
            isset($formData['mollie']['key'])) {

            try {
                // Test the Mollie API key
                $mollie = Mollie::api();
                $mollie->setApiKey($formData['mollie']['key']);
                $mollie->methods->all();

                // If we get here, the API key is valid
            } catch (\Exception $e) {
                Notification::make()
                    ->title('Invalid Mollie API Key')
                    ->body('The provided Mollie API key is invalid. Please check and try again.')
                    ->danger()
                    ->send();

                return;
            }
        }

        $tenant = tenant();

        try {
            $tenant->update($formData);

            Notification::make()
                ->title('Payment settings saved successfully')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Log::error('Failed to save tenant payment settings', ['error' => $e->getMessage()]);

            Notification::make()
                ->title('Failed to save payment settings')
                ->danger()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('save')
                ->label('Save Settings')
                ->action('save')
                ->keyBindings(['command+s', 'ctrl+s']),
        ];
    }
}
