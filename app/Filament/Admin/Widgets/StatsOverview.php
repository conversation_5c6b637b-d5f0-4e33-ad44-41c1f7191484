<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Reservation;
use Carbon\Carbon;
use DateTime;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected ?string $heading = 'Analytics';

    protected ?string $description = 'An overview of some analytics.';

    protected function getStats(): array
    {
        $reservations_today = Reservation::where('created_at', '>=', Carbon::today())
            ->count();

        $reservations_yesterday = Reservation::where('created_at', '>=', Carbon::yesterday())
            ->count();

        $reservations_increase = $reservations_today - $reservations_yesterday;

        return [
            Stat::make('Reservations today '.(new DateTime)->format('H:i'), $reservations_today)
                ->description('Yesterday: '.$reservations_increase),
            // ->descriptionIcon('heroicon-m-arrow-trending-up')
            // ->color('success'),

            Stat::make('Bounce rate', '21%')
                ->description('7% increase')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),
            Stat::make('Average time on page', '3:12')
                ->description('3% increase')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->chart([7, 2, 10, 3, 15, 4, 2])
                ->color('success'),
        ];
    }
}
