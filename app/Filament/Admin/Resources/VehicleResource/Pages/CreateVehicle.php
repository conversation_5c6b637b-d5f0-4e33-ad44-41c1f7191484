<?php

namespace App\Filament\Admin\Resources\VehicleResource\Pages;

use App\Filament\Admin\Resources\VehicleResource;
use App\Models\Place;
use App\Models\Vehicle;
use Filament\Resources\Pages\CreateRecord;

class CreateVehicle extends CreateRecord
{
    protected static string $resource = VehicleResource::class;

    /*
     *
     * Reservation form gives error when a vehicle exists but not a price for its place.
     * If place is in the table, it'll try to get the price foreach vehicle.
     *
     * Systemwise: we should either add a place_vehicle record foreach created vehicle, or set to 0 if not found.
     *
     * I'm going with the second for now. cancelling this.
     *
    */

    /*     protected function afterCreate(): void
        {

            $vehicle_id = $this->record->id;

            // Runs after the form fields are saved to the database.
            $places = Place::all();

            $place_vehicle_array = [];

            foreach($places as $place){}

        } */

}
