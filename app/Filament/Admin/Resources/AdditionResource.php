<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\AdditionResource\Pages;
use App\Models\Addition;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\ToggleColumn;

class AdditionResource extends Resource
{
    protected static ?string $model = Addition::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Extra Services';
    protected static ?int $navigationSort = 15;
    protected static ?string $navigationGroup = 'Instellingen';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')->required()->maxLength(255),
                Textarea::make('description'),
                TextInput::make('price')->required()->maxLength(255),
                Toggle::make('active'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('price')
                    ->state(fn (Addition $addition) => \App\Helpers\Helper::strfmon($addition->price)),
                ToggleColumn::make('active'),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdditions::route('/'),
            'create' => Pages\CreateAddition::route('/create'),
            'edit' => Pages\EditAddition::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return tenant('settings')['additions_enabled'] ?? false;
    }
}
