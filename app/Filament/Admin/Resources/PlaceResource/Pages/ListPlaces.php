<?php

namespace App\Filament\Admin\Resources\PlaceResource\Pages;

use App\Filament\Admin\Resources\PlaceResource;
use App\Models\Vehicle;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ListPlaces extends ListRecords
{
    protected static string $resource = PlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
            ->slideOver()
            ->using(function (array $data, string $model): Model {
                try {
                    // Start a database transaction
                    DB::beginTransaction();

                    // Extract vehicle data from the form data
                    $vehicleData = $data['vehicles'] ?? [];
                    unset($data['vehicles']);

                    // Create the place record
                    $place = $model::create($data);

                    // Get all vehicles to ensure we create records for all of them
                    $vehicles = Vehicle::all();

                    // Create place_vehicle pivot records
                    foreach ($vehicles as $vehicle) {
                        $price = 0; // Default price is 0

                        // If price is provided in the form data, use it
                        if (isset($vehicleData[$vehicle->id]['pivot']['price'])) {

                            $price = $vehicleData[$vehicle->id]['pivot']['price'];

                        }

                        // Create the pivot record
                        DB::table('place_vehicle')->insert([
                            'place_id' => $place->id,
                            'vehicle_id' => $vehicle->id,
                            'price' => $price
                        ]);
                    }

                    // Commit the transaction
                    DB::commit();

                    return $place;
                } catch (\Exception $e) {
                    // If anything goes wrong, rollback the transaction
                    DB::rollBack();
                    throw $e;
                }
            })
            ,
        ];
    }


}
