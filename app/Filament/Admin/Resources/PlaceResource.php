<?php

namespace App\Filament\Admin\Resources;

use App\Enums\VehicleType;
use App\Filament\Admin\Resources\PlaceResource\Pages;
use App\Models\Place;
use App\Models\Vehicle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Forms\Components\Section;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Closure;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use League\Csv\Reader;
use Filament\Forms\Components\FileUpload;
use Illuminate\Support\Facades\Storage;

class PlaceResource extends Resource
{
    protected static ?string $model = Place::class;

    protected static ?string $navigationIcon = 'heroicon-o-paper-airplane';
    protected static ?string $navigationGroup = 'Instellingen';
    protected static ?string $navigationLabel = 'Schiphol prijzen';


    public static function form(Form $form): Form
    {

        return $form->schema([
            Section::make('Plaats')
                ->schema([
                    TextInput::make('name')
                        ->label(
                            fn (Get $get): string => (
                                $get('type') == 'place') ? 'Plaatsnaam' : (
                                    ($get('type') == 'zipcode') ? 'Postcode' :
                                        'Van postcode'
                                )
                        )
                        ->required()
                        ->maxLength(255)
                        ->rules([
                            'required',
                            function (Get $get) {
                                return function (string $attribute, $value, Closure $fail) use ($get) {
                                    if ($get('type') === 'place') {

                                        if (!is_string($value) || is_numeric($value)) {

                                            $fail('Plaatsnaam moet een geldige tekst zijn.');

                                        }

                                    } elseif (in_array($get('type'), ['zipcode', 'zipcoderange'])) {

                                        if (!preg_match('/^\d{4}$/', $value)) {

                                            $fail('Postcode moet uit precies 4 cijfers bestaan.');

                                        }

                                    }
                                };
                            }
                        ]),

                    Select::make('type')
                        ->required()
                        ->options([
                            'place' => 'Plaats',
                            'zipcode' => 'Postcode',
                            'zipcoderange' => 'Postcode range',
                        ])
                        ->default('place')
                        ->live(),

                    TextInput::make('zipcoderange_to')
                        ->label('Tot postcode')
                        ->visible(fn (Get $get): bool => ($get('type') == 'zipcoderange'))
                        ->required()
                        ->maxLength(4)
                        ->rules([
                            function (Get $get) {

                                return function (string $attribute, $value, Closure $fail) use ($get) {
                                    if ($get('type') === 'zipcoderange') {

                                        if (!preg_match('/^\d{4}$/', $value)) {
                                            $fail('Tot postcode moet uit precies 4 cijfers bestaan.');
                                        }

                                        // Check if end zipcode is greater than start zipcode
                                        $startZipcode = (int) $get('name');
                                        $endZipcode = (int) $value;

                                        if ($endZipcode <= $startZipcode) {

                                            $fail('Tot postcode moet groter zijn dan van postcode.');

                                        }

                                    }
                                };
                            }
                        ]),
            ]),
            Section::make('Voortuig prijzen')
                ->schema(
                    Vehicle::query()
                        ->get()
                        ->map(fn (Vehicle $vehicle) =>
                            TextInput::make("vehicles.{$vehicle->id}.pivot.price")
                                ->label($vehicle->name)
                                ->prefix('€')
                                ->numeric()
                                ->default(0)
                                ->afterStateHydrated(function (TextInput $component, $state, $record) use ($vehicle) {
                                    if ($record) {
                                        $component->state($record->vehicles->firstWhere('id', $vehicle->id)?->pivot?->price ?? 0);
                                    }
                                })
                        )
                        ->toArray()
            )
        ]);

    }

    /**
     * Get the base Eloquent query for the table, including eager loads.
     *
     * @return Builder
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['vehicles']); // <-- Eager load the 'customer' and 'items' relationships
            // You can chain other query modifications here too:
            // ->where('status', 'completed')
            // ->orderBy('created_at', 'desc');
    }

    public static function table(Table $table): Table
    {
        // define columns for the table
        $tableColumns = [
            TextColumn::make('name')
                ->searchable()
                ->sortable(),
        ];

        // Foreach vehicle add another column
        $vehicles = Vehicle::all();

        foreach ($vehicles as $key => $vehicle) {

            $tableColumns[] = TextColumn::make('vehicles.' . $key . '.pivot.price')
                ->label($vehicle->name)
                ->money('EUR', locale: 'nl', divideBy: 100);

        }

        return $table
            ->defaultSort('name', 'asc')
            ->columns($tableColumns)
            ->actions([
                EditAction::make()->modal()->slideOver()
                ->using(function (Model $record, array $data): Model {
                    try {
                        DB::beginTransaction();

                        // Extract and remove vehicle data from the form data
                        $vehicleData = $data['vehicles'] ?? [];
                        unset($data['vehicles']);

                        // Update the place record with non-vehicle data
                        $record->update($data);

                        // Update prices in place_vehicle pivot table
                        foreach ($vehicleData as $vehicleId => $vehicle) {
                            DB::table('place_vehicle')
                                ->where('place_id', $record->id)
                                ->where('vehicle_id', $vehicleId)
                                ->update([
                                    'price' => $vehicle['pivot']['price'] ?? 0
                                ]);
                        }

                        DB::commit();
                        return $record;
                    } catch (\Exception $e) {
                        DB::rollBack();
                        throw $e;
                    }
                }),
                DeleteAction::make()
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalDescription('Are you sure you want to delete this place? This will also delete all related price records.'),

            ])
            ->headerActions([
                Action::make('bulkUpdatePrices')
                 ->label('Bulk edit prices')
                 ->icon('heroicon-o-plus-circle') // Optional: Add an icon
                 ->form([

                     Select::make('vehicle_type')
                         ->label(__('Choose Vehicle'))
                         ->options(VehicleType::class) // Fetch vehicles for the dropdown
                         ->required()
                         ->placeholder(__('Select a vehicle')),

                     TextInput::make('amount')
                         ->label(__('Amount to Add'))
                         ->numeric() // Ensures numeric input
                         ->integer() // Ensures integer input
                         ->required()
                         ->helperText(__('Enter the integer amount to add to the current prices (e.g., 500 for €5.00 if storing cents, or 5 if storing euros). Negative numbers will decrease the price.')),

                 ])
                 ->action(function (array $data): void {

                     // Convert VehicleType enum to vehicle ID
                     $vehicleType = $data['vehicle_type'];
                     $vehicleId = VehicleType::getIdFromValue($vehicleType);

                     $amountToAdd = (int) $data['amount']; // Cast to integer

                     if ($amountToAdd === 0) {
                         Notification::make()
                             ->warning()
                             ->title('No Change')
                             ->body('The amount entered was zero. No prices were updated.')
                             ->send();
                         return;
                     }

                     try {
                         // Begin transaction
                         DB::beginTransaction();

                         // Use DB::table for efficiency and the increment method
                         $affectedRows = DB::table('place_vehicle')
                             ->where('vehicle_id', $vehicleId)
                             ->increment('price', $amountToAdd); // Use increment for atomic addition

                         if ($affectedRows > 0) {
                             // Commit the transaction if successful
                             DB::commit();

                             Notification::make()
                                 ->success()
                                 ->title('Prices Updated Successfully')
                                 ->body("Increased prices for {$affectedRows} entries for the selected vehicle by {$amountToAdd}.")
                                 ->send();
                         } else {
                             // Commit the transaction even if no rows were affected
                             // This is not an error condition, just no matching records
                             DB::commit();

                             Notification::make()
                                 ->warning()
                                 ->title('No Prices Updated')
                                 ->body('No matching price entries found for the selected vehicle.')
                                 ->send();
                         }

                         // No need to manually refresh as Filament will handle it
                         // when the action modal is closed

                     } catch (\Exception $e) {
                         // Roll back the transaction if an exception occurs
                         DB::rollBack();

                         Notification::make()
                             ->danger()
                             ->title('Error Updating Prices')
                             ->body('An unexpected error occurred: ' . $e->getMessage())
                             ->send();
                     }
                 }),
                Action::make('importCsv')
                    ->label('Import CSV')
                    ->icon('heroicon-o-document-arrow-up')
                    ->form([
                        FileUpload::make('csv_file')
                            ->label('CSV File')
                            ->disk('local')
                            ->directory('csv-imports')
                            ->acceptedFileTypes(['text/csv', 'application/csv'])
                            ->required()
                            ->helperText('Upload a CSV file with the following format: Place/Zipcode,sedan,stationcar,van'),

                        \Filament\Forms\Components\View::make('filament.forms.components.example-csv-link')
                            ->label('')
                    ])
                    ->action(function (array $data): void {
                        try {
                            $filePath = Storage::disk('local')->path($data['csv_file']);

                            // Parse CSV
                            $csv = Reader::createFromPath($filePath, 'r');
                            $csv->setHeaderOffset(0); // First row contains headers
                            $records = $csv->getRecords();

                            DB::beginTransaction();

                            $updatedCount = 0;
                            $errors = [];

                            // Get vehicle types mapping
                            $vehicleTypes = [
                                'sedan' => VehicleType::SEDAN,
                                'stationcar' => VehicleType::STATION,
                                'van' => VehicleType::VAN
                            ];

                            foreach ($records as $index => $record) {
                                try {
                                    // Get place name/zipcode from the first column
                                    $placeName = trim($record['Place/Zipcode'] ?? '');
                                    if (empty($placeName)) {
                                        $errors[] = "Row " . ($index + 2) . ": Missing place name/zipcode";
                                        continue;
                                    }

                                    // Find place by name
                                    $place = Place::where('name', $placeName)->first();
                                    if (!$place) {
                                        $errors[] = "Row " . ($index + 2) . ": Place '{$placeName}' not found";
                                        continue;
                                    }

                                    // Process each vehicle type column
                                    foreach ($vehicleTypes as $typeName => $vehicleType) {
                                        if (isset($record[$typeName]) && trim($record[$typeName]) !== '') {
                                            // Parse price (remove any non-numeric characters and convert to integer)
                                            $price = (int) preg_replace('/[^0-9]/', '', trim($record[$typeName]));

                                            $vehicleId = VehicleType::getIdFromValue($typeName);

                                            // Update price in pivot table
                                            DB::table('place_vehicle')
                                                ->where('place_id', $place->id)
                                                ->where('vehicle_id', $vehicleId)
                                                ->update(['price' => $price]);

                                            $updatedCount++;
                                        }
                                    }
                                } catch (\Exception $e) {
                                    $errors[] = "Row " . ($index + 2) . ": " . $e->getMessage();
                                }
                            }

                            // Clean up the uploaded file
                            Storage::disk('local')->delete($data['csv_file']);

                            if (count($errors) > 0) {
                                DB::rollBack();

                                Notification::make()
                                    ->danger()
                                    ->title('Import Failed')
                                    ->body('There were errors in your CSV file: ' . implode(', ', array_slice($errors, 0, 3)) .
                                          (count($errors) > 3 ? ' and ' . (count($errors) - 3) . ' more errors' : ''))
                                    ->send();
                            } else {
                                DB::commit();

                                Notification::make()
                                    ->success()
                                    ->title('Import Successful')
                                    ->body("Updated prices for {$updatedCount} entries")
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            DB::rollBack();

                            Notification::make()
                                ->danger()
                                ->title('Import Failed')
                                ->body('An unexpected error occurred: ' . $e->getMessage())
                                ->send();
                        }
                    }),
            ])
            ->defaultPaginationPageOption(50);

    }

    public static function getPages(): array
    {
        return ['index' => Pages\ListPlaces::route('/')];
    }


}
