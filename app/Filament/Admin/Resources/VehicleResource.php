<?php

namespace App\Filament\Admin\Resources;

use App\Enums\VehicleType;
use App\Filament\Admin\Resources\VehicleResource\Pages;
use App\Models\Vehicle;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\TextColumn;

class VehicleResource extends Resource
{
    protected static ?string $model = Vehicle::class;

    protected static ?string $navigationLabel = 'Voertuigen';

    protected static ?int $navigationSort = 10;

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    protected static ?string $navigationGroup = 'Instellingen';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Voertuig')->columns(2)
                    ->schema([

                        Select::make('type')->options(VehicleType::class),

                        TextInput::make('name')->label('Naam')->required()->maxLength(255),

                        Toggle::make('active'),

                ]),

                Section::make('Prijzen')->columns(2)
                    ->schema([

                        TextInput::make('price_km')->prefix('€')->numeric()->required(),

                        TextInput::make('price_min')->prefix('€')->numeric()->required(),

                        TextInput::make('price_start')->prefix('€')->numeric()->required(),

                        TextInput::make('price_waypoint')->prefix('€')->numeric()->required(),

                        TextInput::make('price_minimum')->prefix('€')->numeric()->required(),

                ]),

                Section::make('Capaciteit')->columns(2)
                    ->schema([

                        TextInput::make('max_people')->required(),

                        TextInput::make('max_luggage')->required(),

                        TextInput::make('max_handluggage')->required(),

                        // if combined filled in, the combination of luggage+handluggage cannot exceed this
                        TextInput::make('max_combined_luggage')->required(),

                ]),

                Section::make('Labels')->columns(2)
                    ->schema([

                        TextInput::make('label_1')->required(),

                        TextInput::make('label_2')->required(),

                        TextInput::make('label_3')->required(),

                        TextInput::make('label_4')->required(),

                ])

            ]);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                TextColumn::make('name'),

                ToggleColumn::make('active')
                    ->label('Active Status')
                    ->onColor('success') // Green when active
                    ->offColor('danger'), // Red when inactive

                TextColumn::make('price_km')
                    ->money('EUR', locale: 'nl', divideBy: 100),

                TextColumn::make('price_min')
                    ->money('EUR', locale: 'nl', divideBy: 100),

                TextColumn::make('price_start')
                    ->money('EUR', locale: 'nl', divideBy: 100),

                TextColumn::make('price_waypoint')
                    ->money('EUR', locale: 'nl', divideBy: 100),

                TextColumn::make('price_minimum')
                    ->money('EUR', locale: 'nl', divideBy: 100),


                    ])

            ->actions([
                Tables\Actions\EditAction::make(),
            ])

            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);

    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicles::route('/'),
            'create' => Pages\CreateVehicle::route('/create'),
            'edit' => Pages\EditVehicle::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

}
