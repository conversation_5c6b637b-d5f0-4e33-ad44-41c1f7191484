<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ReservationResource\Pages;
use App\Models\Reservation;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Tables\Columns\TextColumn;

class ReservationResource extends Resource
{
    protected static ?string $model = Reservation::class;

    protected static ?int $navigationSort = 0;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('user_id')
                    ->relationship('user', 'name'),
                DateTimePicker::make('datetime')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('rand_id')
                    ->label(__('id')),
                Tables\Columns\TextColumn::make('data.user.name')
                    ->label(__('Klant'))
                    ->state(fn (Reservation $reservation) => $reservation->data['user']['name'].'<br>'.
                        $reservation->data['user']['phone'].'<br>'.
                        $reservation->data['user']['email']
                    )
                    ->html(),
                Tables\Columns\TextColumn::make('datetime')
                    ->dateTime('d.m.Y H:i')
                    ->label(__('Ophaaldatum')),
                // show payment status if online paid.
                Tables\Columns\TextColumn::make('data.payment_status')
                    ->label(__('Betaald'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'canceled' => 'danger',
                        default => 'secondary',
                    })
                // Tables\Columns\TextColumn::make('created_at')
                //     ->since()
                //     ->label(__('Booked on')),
            ])
            ->filters([
                //
            ])
            ->defaultSort('id', 'desc');

    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReservations::route('/'),
            'view' => Pages\ViewReservation::route('/{record}'),
        ];
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }

    public static function canCreate(): bool
    {
        return false;
    }
}
