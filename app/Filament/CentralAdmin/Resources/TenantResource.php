<?php

namespace App\Filament\CentralAdmin\Resources;

use App\Filament\CentralAdmin\Resources\TenantResource\Pages;
use App\Models\Tenant;
use App\Rules\FQDN;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Mail;

class TenantResource extends Resource
{
    protected static ?string $model = Tenant::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->columnSpanFull(),
                // Forms\Components\DateTimePicker::make('created_at'),
                // Forms\Components\DateTimePicker::make('updated_at'),
                Forms\Components\KeyValue::make('settings')
                    ->default(config('tenancy.tenant_data_template.settings')),
                Forms\Components\KeyValue::make('contact')
                    ->default(config('tenancy.tenant_data_template.contact')),
                Forms\Components\KeyValue::make('googlemaps')
                    ->default(config('tenancy.tenant_data_template.googlemaps')),
                Forms\Components\KeyValue::make('google-calendar')
                    ->default(config('tenancy.tenant_data_template.google-calendar')),
                Forms\Components\KeyValue::make('mollie')
                    ->default(config('tenancy.tenant_data_template.mollie')),
                Forms\Components\KeyValue::make('mail')
                    ->default(config('tenancy.tenant_data_template.mail')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordUrl(null) // disableing whole clickable rows.
            ->defaultSort('created_at', 'DESC')
            ->columns([

                Tables\Columns\TextColumn::make('created_at')
                    ->since()->sortable()->toggleable(isToggledHiddenByDefault: false)
                    ->extraHeaderAttributes(['style' => 'width:100px'])
                    ->size(TextColumn\TextColumnSize::ExtraSmall)
                    ->badge()->color('gray'),

                Tables\Columns\TextColumn::make('updated_at')->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('id')->label('ID')->searchable()
                    ->weight(FontWeight::Bold),

                Tables\Columns\TextColumn::make('domains')
                    ->state(function($record){
                        return $record->domains->pluck('domain')
                            ->map(fn($domain) => '<a href="//'.$domain.'" target="_blank" style="text-align:left;"><span style="--c-50:var(--primary-50);--c-400:var(--primary-400);--c-600:var(--primary-600);" class="fi-badge items-center justify-center gap-x-1 rounded-md text-xs font-medium ring-1 ring-inset px-2 min-w-[theme(spacing.6)] py-1 fi-color-custom bg-custom-50 text-custom-600 ring-custom-600/10 dark:bg-custom-400/10 dark:text-custom-400 dark:ring-custom-400/30 fi-color-primary inline!">'.$domain.'</span></a>')
                            ->join(' ');
                    })->html()
                    ->wrapHeader(),

            ])->modifyQueryUsing(fn (Builder $query) => $query->with('domains'))
            ->actions([
                Tables\Actions\EditAction::make()
                    ->button(),

                Tables\Actions\Action::make('add Domain')
                    ->button()
                    ->icon('heroicon-m-globe-alt')
                    ->form([
                        Forms\Components\TextInput::make('domain')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Enter Domainname')
                            ->rules(['required', 'string', new FQDN]),
                    ])
                    ->action(function (array $data, Tenant $tenant): void {

                        $tenant->domains()->create([
                            'domain' => $data['domain'],
                        ]);

                    })
                    ->slideOver(),

                Tables\Actions\Action::make('test Mail')
                    ->button()
                    ->icon('heroicon-m-envelope')
                    ->form([
                        Forms\Components\TextInput::make('send_to')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Send E-mail to which address')
                            ->default('<EMAIL>')
                            ->rules(['required', 'email']),
                        Forms\Components\Textarea::make('message')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Message')
                            ->default('Hello world!')
                            ->rules(['required', 'string']),
                    ])
                    ->action(function (array $data, Tenant $tenant): void {

                        $tenant->run(function() use($data) {

                            Mail::raw($data['message'], function($message) use($data) {
                                $message->to($data['send_to'])->subject('ikBoek Mail Test');

                                // Debug the transport being used
                                // $transport = app('mail.manager')->mailer()->getSymfonyTransport();
                                // dump([
                                //     'transport_class' => get_class($transport),
                                //     'mailer_class' => get_class(app('mail.manager')->mailer()),
                                //     'mailer_name' => app('mail.manager')->getDefaultDriver(),
                                //     'config' => config('mail.mailers.' . app('mail.manager')->getDefaultDriver())
                                // ]);
                            });

                        });

                    })
                    ->slideOver(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTenants::route('/'),
            'create' => Pages\CreateTenant::route('/create'),
            'edit' => Pages\EditTenant::route('/{record}/edit'),
        ];
    }

}
