<?php

namespace App\Filament\CentralAdmin\Resources\TenantResource\Pages;

use App\Filament\CentralAdmin\Resources\TenantResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Password;

class CreateTenant extends CreateRecord
{

    protected static string $resource = TenantResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {

        // Merge with the data template which is in the tenancy config file.
        // The latter has higher priority.

        return array_merge(config('tenancy.tenant_data_template'), $data);

    }

    // At this point the tenant and db are created and seeded.
    protected function afterCreate(): void
    {
        // $tenant = $this->getRecord();
        // Password::sendResetLink($tenant->mail['from_address']);

    }

}
