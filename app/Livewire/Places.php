<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Http;
use Livewire\Component;

class Places extends Component
{
    public $query = '';
    public array $results = [];
    public bool $showResults = false;
    public int $selectedIndex = 0;

    public function updatedQuery()
    {
        $this->selectedIndex = 0;

        if (strlen($this->query) < 3) {
            $this->results = [];
            $this->showResults = false;
            return;
        }

        $apiKey = config('services.google.maps_api_key');
        if (!$apiKey) {
            $this->results = [];
            $this->showResults = false;
            return;
        }

        $response = Http::get('https://maps.googleapis.com/maps/api/place/autocomplete/json', [
            'input' => $this->query,
            'key' => $apiKey,
        ]);

        if ($response->successful()) {
            $this->results = $response->json()['predictions'];
            $this->showResults = count($this->results) > 0;
        } else {
            $this->results = [];
            $this->showResults = false;
        }
    }

    public function selectResult()
    {
        if (isset($this->results[$this->selectedIndex])) {
            $selectedPlace = $this->results[$this->selectedIndex];
            $this->query = $selectedPlace['description'];
            $this->emit('placeSelected', $selectedPlace['place_id']);
            $this->showResults = false;
        }
    }

    public function render()
    {
        return view('livewire.places');
    }
}