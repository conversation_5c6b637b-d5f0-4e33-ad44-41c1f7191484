<?php

namespace App\Livewire\Steps;

use App\Livewire\CustomStepComponent;

use <PERSON>tie\LivewireWizard\Components\StepComponent;
use Illuminate\Support\Facades\DB;
use Closure;
final class SecondStepComponent extends CustomStepComponent
{

    public $people = 1;

    public $luggage = 0;

    public $handluggage = 0;

    public function mount()
    {
        $this->state()->setStepState();

        $this->dispatch('scroll-to-top');
    }

    public function render()
    {

        $max_vehicle_capacities = \App\Models\Vehicle::active()
            ->select([DB::raw('MAX(max_people) AS max_people'), DB::raw('MAX(max_luggage) AS max_luggage'), DB::raw('MAX(max_handluggage) AS max_handluggage'), DB::raw('MAX(max_combined_luggage) AS max_combined_luggage')])
            ->first();

        return view('livewire.reservation-wizard.steps.second', [
            'luggage_count' => range(0, $max_vehicle_capacities->max_luggage),
            'handluggage_count' => range(0, $max_vehicle_capacities->max_handluggage),
            'people_count' => range(1, $max_vehicle_capacities->max_people),
        ]);
    }

    public function submit()
    {

        $this->validate($this->rules());

        $this->nextStep();

    }

    public function rules()
    {
        $max_vehicle_capacities = \App\Models\Vehicle::active()
            ->select([
                DB::raw('MAX(max_people) AS max_people'),
                DB::raw('MAX(max_luggage) AS max_luggage'),
                DB::raw('MAX(max_handluggage) AS max_handluggage'),
                DB::raw('MAX(max_combined_luggage) AS max_combined_luggage')
            ])
            ->first();

        return [
            'luggage' => [
                'required',
                'integer',
                'min:0',
                'max:' . $max_vehicle_capacities->max_luggage,
                function ($attribute, $value, Closure $fail) use ($max_vehicle_capacities) {
                    if ($value + $this->handluggage > $max_vehicle_capacities->max_combined_luggage) {
                        $fail('Onze maximum capaciteit bedraagt ' . $max_vehicle_capacities->max_combined_luggage . ' stukken bagage in totaal (Bagage + Handbagage).
                        <br>Op dit moment zijn het er: '.$this->luggage.' (Bagage) + '.$this->handluggage.' (Handbagage) = <b>'. ($this->luggage + $this->handluggage).'</b>');
                    }
                },
            ],
            'handluggage' => [
                'required',
                'integer',
                'min:0',
                'max:' . $max_vehicle_capacities->max_handluggage,
                function ($attribute, $value, Closure $fail) use ($max_vehicle_capacities) {
                    if ($value + $this->luggage > $max_vehicle_capacities->max_combined_luggage) {
                        $fail('Onze maximum capaciteit bedraagt ' . $max_vehicle_capacities->max_combined_luggage . ' stukken bagage in totaal (Bagage + Handbagage).
                        <br>Op dit moment zijn het er: '.$this->luggage.' (Bagage) + '.$this->handluggage.' (Handbagage) = <b>'. ($this->luggage + $this->handluggage).'</b>');
                    }
                },
            ],
            'people' => [
                'required',
                'integer',
                'min:1',
                'max:' . $max_vehicle_capacities->max_people,
            ],
        ];
    }

    public function stepInfo(): array
    {
        return [
            'label' => 'Bagage',
            'icon' => '<path fill-rule="evenodd" d="M7.5 5.25a3 3 0 013-3h3a3 3 0 013 3v.205c.933.085 1.857.197 2.774.334 1.454.218 2.476 1.483 2.476 2.917v3.033c0 1.211-.734 2.352-1.936 2.752A24.726 24.726 0 0112 15.75c-2.73 0-5.357-.442-7.814-1.259-1.202-.4-1.936-1.541-1.936-2.752V8.706c0-1.434 1.022-2.7 2.476-2.917A48.814 48.814 0 017.5 5.455V5.25zm7.5 0v.09a49.488 49.488 0 00-6 0v-.09a1.5 1.5 0 011.5-1.5h3a1.5 1.5 0 011.5 1.5zm-3 8.25a.75.75 0 100-********* 0 000 1.5z" clip-rule="evenodd" /><path d="M3 18.4v-2.796a4.3 4.3 0 00.713.31A26.226 26.226 0 0012 17.25c2.892 0 5.68-.468 8.287-1.335.252-.084.49-.189.713-.311V18.4c0 1.452-1.047 2.728-2.523 2.923-2.12.282-4.282.427-6.477.427a49.19 49.19 0 01-6.477-.427C4.047 21.128 3 19.852 3 18.4z" />',
        ];
    }

}
