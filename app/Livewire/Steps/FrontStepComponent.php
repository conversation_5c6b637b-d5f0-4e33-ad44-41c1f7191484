<?php

namespace App\Livewire\Steps;

use App\Helpers\CostCalculator;
use App\Helpers\GoogleMapsHelper;
use App\Models\Address;
use Illuminate\Http\Request;
use Livewire\Attributes\On;
use App\Livewire\CustomStepComponent;

final class FrontStepComponent extends CustomStepComponent
{
    public $waypoints; // input

    public $waypoint_data = []; // geocoding result with address_components

    public $datetime;

    public $flightnr;

    public $retour = false;

    public $retour_datetime;

    public $retour_flightnr;

    public $prices;

    public $has_waypoints = false;

    public $distance;

    public $duration; // has at least 1 waypoint besides origin/destination

    public $addresses = false;

    public $showChooseAddressModal = false;

    public $waypoint_currently_editing;

    public $chosenAddressId; // for chooseFromAddressesModal

    public $origin_is_airport = false;

    public $destination_is_airport = false;

    public function mount(Request $request)
    {
        // put current state in session with each new step.
        // $request->session()->put('state', $this->state()->all());
        $request->session()->forget('state');

        if (auth()->check()) {

            if (! auth()->user()->addresses->isEmpty()) {
                $this->addresses = auth()->user()->addresses;
            }

        }

        $this->waypoints = [
            // origin
            'origin' => $this->getWaypointElement(['name' => 'origin', 'label' => 'van']),

            // destination
            'destination' => $this->getWaypointElement(['name' => 'destination', 'label' => 'naar',
                'icon' => '<span class="flex items-center justify-center w-8 h-8 bg-green-500 rounded-full ring-8 ring-white">
            <svg class="w-5 h-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
            </svg>
        </span>', ]),
        ];
    }

    public function render()
    {
        return view('livewire.reservation-wizard.steps.front');
    }

    /*
     * This method is only fired when an address from googleAutocomplete is chosen.
     * In such case, also remove possible address_id from waypoint.
    */
    #[On('waypoint-updated')]
    public function waypointUpdated($key, $address)
    {
        // If customer wants to be pickedup from an airport, then let them type in flightnr.
        // if key == 0, so 'from'
        if ($key == 'origin') {
            if (str_contains(strtolower($address), 'schiphol') || str_contains(strtolower($address), 'airport')) {
                $this->origin_is_airport = true;
            }

            // if key is last so destination. Then let them choose 'retour'
        } elseif ($key == 'destination') {
            if (str_contains(strtolower($address), 'schiphol') || str_contains(strtolower($address), 'airport')) {
                $this->destination_is_airport = true;
            }
        }

        $this->waypoints[$key]['value'] = $address;

        // to remove possible address_id from this waypoint.
        unset($this->waypoints[$key]['address_id']);

    }

    // This submit function uses js to only get waypoints in as argument.
    // So other fields are retrieved via livewire models.
    public function submit($formData)
    {

        // to prevent submission wo date & time
        $this->validate();

        // We only remove unwanted variables from the $formData submitted formdata. The datetime etc data is already stored in the global variables. eg: $this->datetime.
        // Now we are left with only the waypoints.
        unset($formData['datetime'], $formData['flightnr'], $formData['retour'], $formData['retour_datetime'], $formData['retour_flightnr']);



        foreach ($formData as $key => $waypoint) {

            /*
             * $this->waypoints is from input.
             * $this->waypoint_data is data from GoogleMaps.
             */

            // if chosen from Addressbook get address
            if (isset($this->waypoints[$key]['address_id'])) {

                $address_json = Address::where('user_id', auth()->user()->id)->where('id', $this->waypoints[$key]['address_id'])->first()->address;
                $this->waypoint_data[$key] = json_decode($address_json, true);

            }

            // if submitted field is empty, return error.
            else {


                if (empty($waypoint)){

                    $this->addError($key, $waypoint . '<br> Dit adres is leeg.');
                    return;
                }

                $this->waypoint_data[$key] = GoogleMapsHelper::getPlace($waypoint);

                // if empty str (/erroneous address), Gmaps response is void/null. Directly throw error and dont continue
                if (! $this->waypoint_data[$key]){

                    $this->addError($key, 'Dit adres is niet compleet. GoogleMaps null response.');
                    return;

                }

            }

            // check if all data is correctly filled in
            if (
                $this->waypoint_data[$key]['type'] == 'premise' ||
                $this->waypoint_data[$key]['type'] == 'route' ||
                $this->waypoint_data[$key]['type'] == 'street_address' ||
                $this->waypoint_data[$key]['type'] == 'postal_code'
            ) {

                // check if not schiphol
                $address_str = strtolower(implode(' ', $this->waypoint_data[$key]));
                if (! str_contains($address_str, 'schiphol')) {

                    if (! isset($this->waypoint_data[$key]['street_number'])) {
                        $this->addError($key, 'Dit adres mist een huisnummer.');
                    }

                }
            }

            if (! isset($this->waypoint_data[$key]['locality'])) {
                $this->addError($key, 'Dit adres is niet compleet. Geen plaats.');
            } // plaats

            if (! isset($this->waypoint_data[$key]['postal_code'])) {
                $this->addError($key, 'Dit adres is niet compleet. Geen postcode.');
            } // postcode

            // if there is any error, dont continue, but return with errorbag to form.
            if (! $this->getErrorBag()->isEmpty()) return;

        }

        $directions = GoogleMapsHelper::getDirections( $this->waypoints );

        $this->distance = $directions['distance'];
        $this->duration = $directions['duration'];

        $this->prices = (new CostCalculator($directions, $this->waypoint_data))->calculate();

        $this->nextStep();

    }

    public function addWaypoint()
    {
        // store origin & destination in variables, and remove from waypoints array.
        $origin = array_shift($this->waypoints);
        $destination = array_pop($this->waypoints);

        $newKey = count($this->waypoints) + 1; // count existing waypoints minus origin/destination

        // add new waypoint to waypoints-array
        $this->waypoints = array_merge(

            ['origin' => $origin],

            $this->waypoints, // existing waypoints

            [
                'waypoint_'.$newKey => $this->getWaypointElement(['name' => 'waypoint_'.$newKey, 'label' => 'via', 'is_waypoint' => true]),
                'destination' => $destination,
            ]
        );

    }

    public function removeWaypoint($key)
    {
        unset($this->waypoints[$key]);
    }

    /**
     * getWaypointElement
     *
     * @param  array  $waypoint_info
     *                                example: ['name' =>, 'label'=>, 'icon' => false, 'value' = null]
     * @return void
     */
    public function getWaypointElement(array $waypoint_info)
    {
        return [
            'name' => $waypoint_info['name'],
            'label' => $waypoint_info['label'],
            'icon' => $waypoint_info['icon'] ?? '
            <span class="flex items-center justify-center w-8 h-8 bg-gray-400 rounded-full ring-8 ring-white">
                <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-white">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                </svg>
            </span>',
            'value' => $waypoint_info['value'] ?? '',
            'is_waypoint' => isset($waypoint_info['is_waypoint']),
        ];
    }

    /*
    * Set editing_waypoint_name,
    * Open modal with select with addresses.
    * accepts key for $this->waypoints array.
    */
    public function chooseAddress()
    {

        $address = Address::where('user_id', auth()->user()->id)->where('id', $this->chosenAddressId)->first();

        if ($address) {

            $this->waypoints[$this->waypoint_currently_editing]['value'] = $address->type == 'airport' ? $address->locality : $address->route.' '.$address->street_number;

            $this->waypoints[$this->waypoint_currently_editing]['address_id'] = $address->id;

        }

        $this->showChooseAddressModal = false;

    }

    public function setWaypointCurrentlyEditing($key)
    {
        $this->waypoint_currently_editing = $key;
        $this->showChooseAddressModal = true;

    }

    public function rules()
    {
        $rules = ['datetime' => 'required'];

        if ($this->retour) {
            $rules['retour_datetime'] = 'required';
        }

        $rules = ['flightnr' => 'string|max:10|nullable'];
        $rules = ['retour_flightnr' => 'string|max:10|nullable'];

        return $rules;
    }

    public function messages()
    {
        return [
            'datetime.required' => 'Ophaalmoment is verplicht',
            'datetime_retour.required' => 'Ophaalmoment retour is verplicht',
        ];
    }

    public function stepInfo(): array
    {
        return [
            'label' => 'Reisgegevens',
            'icon' => '<path fill-rule="evenodd" d="M11.54 22.351l.07.04.028.016a.76.76 0 00.723 0l.028-.015.071-.041a16.975 16.975 0 001.144-.742 19.58 19.58 0 002.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 00-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 002.682 2.282 16.975 16.975 0 001.145.742zM12 13.5a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />',
        ];
    }

}
