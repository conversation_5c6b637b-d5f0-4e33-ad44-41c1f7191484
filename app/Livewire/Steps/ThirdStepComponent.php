<?php

namespace App\Livewire\Steps;

use App\Livewire\CustomStepComponent;
use App\Models\Vehicle;

final class ThirdStepComponent extends CustomStepComponent
{
    public $payment_method = 'Pin';

    public $chosen_vehicle_id;

    public function mount()
    {
        // put current state in session with each new step.
        $this->state()->setStepState();

        $this->dispatch('scroll-to-top');

    }

    public function render()
    {
        return view('livewire.reservation-wizard.steps.third', [
            'vehicles' => $this->getVehicles(),
            'count' => range(1, 8),
            'prices' => $this->state()->prices(),
            'payment_methods' => ['Pin', 'Cash', 'Creditcard', 'Tikkie', 'Apple pay'],
            'vehicle_theme' => tenant('settings')['vehicle_theme'],
        ]);
    }

    public function submit($vehicleId = null)
    {
        if ($vehicleId) {
            $this->chosen_vehicle_id = $vehicleId;
        }

        $this->validate();

        $this->nextStep();

    }

    public function getVehicles()
    {
        $people = $this->allStepsState['second']['people'];
        $luggage = $this->allStepsState['second']['luggage'];
        $handluggage = $this->allStepsState['second']['handluggage'];

        $vehicles = Vehicle::where('active', true)
            ->where('max_people', '>=', $people)
            ->where('max_luggage', '>=', $luggage)
            ->where('max_handluggage', '>=', $handluggage)
            ->where('max_combined_luggage', '>=', $handluggage + $luggage)
            ->get()->keyBy('id');

        $vehicle_ids = $vehicles->pluck('id');

        if (! $vehicle_ids->contains($this->chosen_vehicle_id)) {
            $this->chosen_vehicle_id = $vehicles->keys()->first();
        }

        return $vehicles;
    }

    public function rules()
    {
        return [
            'chosen_vehicle_id' => 'required|integer',
        ];
    }

    public function messages()
    {
        return [
            'chosen_vehicle_id' => 'Kies aub een voertuig.',
        ];
    }

    public function stepInfo(): array
    {
        return [
            'label' => 'Aanbiedingen',
            'icon' => '<svg viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6"><path fill-rule="evenodd" d="M5.25 2.25a3 3 0 00-3 3v4.318a3 3 0 00.879 2.121l9.58 9.581c.92.92 2.39 1.186 3.548.428a18.849 18.849 0 005.441-5.44c.758-1.16.492-2.629-.428-3.548l-9.58-9.581a3 3 0 00-2.122-.879H5.25zM6.375 7.5a1.125 1.125 0 100-2.25 1.125 1.125 0 000 2.25z" clip-rule="evenodd" /></svg>',
        ];
    }
}
