<?php

namespace App\Livewire\Steps;

use App\Enums\ReservationStatus;
use App\Events\ReservationCreated;
use App\Http\Controllers\MolliePaymentController;
use App\Livewire\CustomStepComponent;
use App\Models\Addition;
use App\Models\Reservation;
use App\Models\Vehicle;
use Livewire\Attributes\On;
use Livewire\Attributes\Validate;

final class FourthStepComponent extends CustomStepComponent
{
    public $chosen_vehicle_id;

    public $chosen_vehicle_name;

    public $additions;

    public $chosenAdditionsCount = 0;

    public $chosenAdditionsTotal = 0;

    #[Validate('required|string|min:3', message: 'Het naam veld is verplicht.')]
    public $name;

    #[Validate('required|string|min:8', message: 'Het telefoonnummer veld is verplicht.')]
    public $phone;

    public string $phoneValidationError = '';

    #[Validate('required|string|email', message: 'Het E-mailadres veld is verplicht.')]
    public $email;

    public $comments;

    public $password;

    public $price;

    public $fare;

    public $showOnlinePayment = false;
    public $onlinePaymentRequired = false;

    public function mount()
    {
        $this->state()->setStepState();

        if (auth()->check()) {
            $this->fill(auth()->user());
        }

        $this->chosen_vehicle_id = $this->allStepsState['third']['chosen_vehicle_id'];
        $this->chosen_vehicle_name = Vehicle::find($this->chosen_vehicle_id)->name;

        // Ritprijs
        $this->fare = $this->state()->prices()[$this->chosen_vehicle_id]['subtotal'];

        // Check if online payment should be shown based on limits
        $this->checkOnlinePaymentLimits();

        $this->dispatch('scroll-to-top');

        $this->additions = Addition::active()->get()->keyBy('id')->toArray();

        // dump('distance: ' . $this->allStepsState['front']['distance']);
        // dump('online payment limit: ' . tenant('payment')['online_payment_limit_value']);

    }

    protected function checkOnlinePaymentLimits()
    {
        // Default to not showing online payment
        $this->showOnlinePayment = false;
        $this->onlinePaymentRequired = false;

        // First check if online payment is enabled at all
        if (!(tenant('payment')['enable_online_payment'] ?? false)) {
            return;
        }

        // If online payment is enabled, it's at least optional
        $this->showOnlinePayment = true;

        $limitType = tenant('payment')['online_payment_limit_type'] ?? 'none';
        $limitValue = (float)(tenant('payment')['online_payment_limit_value'] ?? 0);

        // If no limit is set, online payment remains optional
        if ($limitType === 'none' || $limitValue <= 0) {
            return;
        }

        // Check distance limit
        if ($limitType === 'distance') {
            $distance = round($this->allStepsState['front']['distance'] ?? 0);
            $this->onlinePaymentRequired = $distance >= $limitValue;
        }

        // Check price limit
        if ($limitType === 'price') {
            $this->onlinePaymentRequired = ($this->price / 100) >= $limitValue; // Convert cents to euros for comparison
        }
    }

    public function render()
    {
        $this->updatePrice();
        return view('livewire.reservation-wizard.steps.fourth', [
            'waypoint_data' => $this->state()->waypointData(),
            'reservation_data' => $this->state()->reservationData(),
            'additions' => $this->additions
        ]);
    }

    public function updatePrice()
    {
        $this->fare = $this->state()->prices()[$this->chosen_vehicle_id]['subtotal'];

        $this->price = $this->fare + $this->chosenAdditionsTotal;
        if($this->state()->reservationData()['retour']) $this->price *= 2;

        $this->checkOnlinePaymentLimits();

        return $this->price;
    }

    public function submit($withPayment = false)
    {
        // If online payment is required but user tries to submit without payment, show error
        if ($this->onlinePaymentRequired && !$withPayment) {
            $this->addError('payment', 'Online betaling is verplicht voor deze reservering.');
            return;
        }

        // if intl_phone_enabled, check if phone is valid
        if (tenant('settings')['intl_phone_enabled'] ?? false) {

            if (! empty($this->phoneValidationError)) {

                $this->addError('phone', $this->phoneValidationError);

                return;
            }

        }

        $this->validate();

        $data = $this->allStepsState;

        $user_id = auth()->check() ? auth()->user()->id : null;

        // update phone number if empty or changed. Because when registering that is the only one which can be empty. Name & email should already be set.
        if ( auth()->check() && $this->phone !== auth()->user()->phone ) {
            auth()->user()->phone = $this->phone;
            auth()->user()->save();
        }

        $price = $this->updatePrice();

        // Reservation data for the reservation and possible retour.
        $common_reservation_data = [

            'waypoints' => $data['front']['waypoint_data'],
            'vehicle_id' => $this->chosen_vehicle_id,
            'vehicle_name' => $this->chosen_vehicle_name,
            'flightnr' => $data['front']['retour_flightnr'],
            'people' => $data['second']['people'],
            'payment_method' => $withPayment ? 'mollie' : $data['third']['payment_method'] ?? null,
            'luggage' => $data['second']['luggage'],
            'handluggage' => $data['second']['handluggage'],
            'comment' => $this->comments,
            'distance' => round($data['front']['distance']),
            'duration' => ceil($data['front']['duration']),
            'price' => $price,

            'user' => [
                'name' => $this->name,
                'phone' => $this->phone,
                'email' => $this->email,
            ],

        ];

        // if has additions put those
        if ($this->chosenAdditionsCount) {

            $common_reservation_data['additions'] = $this->additions;
            $common_reservation_data['chosenAdditionsCount'] = $this->chosenAdditionsCount;
            $common_reservation_data['chosenAdditionsTotal'] = $this->chosenAdditionsTotal;

        }

        // RETOUR: if has retour, first create retour-reservation
        if ($data['front']['retour']) {

            $retour = new Reservation;

            $retour->user_id = $user_id;
            $retour->rand_id = Reservation::generateRandId();
            $retour->datetime = $data['front']['retour_datetime'];
            $retour->data = $common_reservation_data;
            $retour->data['waypoints'] = array_reverse($data['front']['waypoint_data']);
            $retour->data['is_retour'] = 1;

            // if withPayment, set status to temporary. Changes to new when payment is completed.
            if ($withPayment) $retour->status = ReservationStatus::TEMPORARY;

            $retour->save(); // Todo: try/catch if not saved, exception retour not saved.

        }

        // Create the actual Reservation

        $reservation = new Reservation;

        $reservation->user_id = $user_id;
        $reservation->rand_id = Reservation::generateRandId();
        $reservation->datetime = $data['front']['datetime'];
        $reservation->data = $common_reservation_data;

        // if withPayment, set status to temporary. Changes to new when payment is completed.
        if ($withPayment) $reservation->status = ReservationStatus::TEMPORARY;

        if ($data['front']['retour'] && ! empty($retour)) {
            $reservation->data['retour_id'] = $retour->id;
        }

        $reservation->save(); // Todo: try/catch if not saved, exception reservation not saved.

        ReservationCreated::dispatch($reservation);

        if ($data['front']['retour'] && ! empty($retour)) {

            ReservationCreated::dispatch($retour);

        }

        if ($withPayment) {

            // directly handle the molliePaymentController request in here.
            MolliePaymentController::checkout($reservation->id);

        }else{

            // if no online/mollie: complete/success the reservation.
            redirect()->route('reservation', ['reservation_rand_id' => $reservation->rand_id]);

        }

    }

    public function updatedAdditions()
    {
        // Reset the variables
        $this->chosenAdditionsTotal = 0;
        $this->chosenAdditionsCount = 0;

        foreach ($this->additions as $addition) {

            // Cumulate the prices of chosen additions into $chosenAdditionsTotal
            if (@$addition['selected']) {
                $this->chosenAdditionsTotal += $addition['price'];
            }

            // Cumulate the prices of chosen additions count into chosenAdditionsCount
            $this->chosenAdditionsCount++;

        }

    }

    // Used for telinput
    #[On('phone-updated')]
    public function handlePhoneUpdate($phone)
    {
        // Set phoneValidationError to empty str, Because update is only possible when valid.
        $this->phoneValidationError = '';
        $this->resetValidation('phone');
        $this->phone = $phone;
    }

    #[On('phone-validation-error-updated')]
    public function handlePhoneValidationError($message)
    {
        $this->phoneValidationError = $message;
        $this->addError('phone', $this->phoneValidationError);
    }

    public function stepInfo(): array
    {
        return [
            'label' => 'Contactgegevens',
            'icon' => '<svg viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6"><path fill-rule="evenodd" d="M6.75 2.25A.75.75 0 017.5 3v1.5h9V3A.75.75 0 0118 3v1.5h.75a3 3 0 013 3v11.25a3 3 0 01-3 3H5.25a3 3 0 01-3-3V7.5a3 3 0 013-3H6V3a.75.75 0 01.75-.75zm13.5 9a1.5 1.5 0 00-1.5-1.5H5.25a1.5 1.5 0 00-1.5 1.5v7.5a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5v-7.5z" clip-rule="evenodd" /></svg>',
        ];
    }
}
