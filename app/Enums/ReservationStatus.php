<?php
namespace App\Enums;

enum ReservationStatus: string
{
    case TEMPORARY = 'temporary';
    case NEW = 'new';
    case IN_PROGRESS = 'in_progress';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';

    /**
     * Get the label for the enum value.
     *
     * @return string
     */
    public function label(): string
    {
        return match($this) {
            self::TEMPORARY => 'Temporary',
            self::NEW => 'New',
            self::IN_PROGRESS => 'In Progress',
            self::COMPLETED => 'Completed',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * Get all enum values as an array.
     *
     * @return array
     */
    public static function toArray(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all enum values with their labels as an array.
     *
     * @return array
     */
    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn ($case) => [
            $case->value => $case->label()
        ])->all();
    }

    /**
     * Check if the status can transition to the given status.
     *
     * @param ReservationStatus $status
     * @return bool
     */
    public function canTransitionTo(ReservationStatus $status): bool
    {
        return match($this) {
            self::TEMPORARY => in_array($status, [self::NEW, self::CANCELLED]),
            self::NEW => in_array($status, [self::IN_PROGRESS, self::COMPLETED, self::CANCELLED]),
            self::IN_PROGRESS => in_array($status, [self::COMPLETED, self::CANCELLED]),
            self::COMPLETED => false,
            self::CANCELLED => false,
        };
    }
}
