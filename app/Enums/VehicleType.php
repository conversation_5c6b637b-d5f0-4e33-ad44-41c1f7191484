<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum VehicleType: string implements HasLabel
{
    case SEDAN = 'sedan';
    case STATION = 'stationcar';
    case VAN = 'van';


    /**
     * getLabel
     *
     * Required for Filamentphp Select labels.
     *
     * @return string
     */
    public function getLabel(): string
    {
        return $this->value;
    }

    /**
     * Get ID from string value
     * eg: sedan => 1
     *
     * @param string $value
     * @return int|null
     */
    public static function getIdFromValue(string $value): ?int
    {
        try {
            $case = self::from($value);
            return $case->getId();
        } catch (\ValueError $e) {
            return null;
        }
    }

    /**
     * Get enum from ID
     *
     * @param int $id
     * @return self|null
     */
    public static function fromId(int $id): ?self
    {
        return match ($id) {
            1 => self::SEDAN,
            2 => self::STATION,
            3 => self::VAN,
            default => null,
        };
    }

    /**
     * Get ID for this enum case
     *
     * @return int
     */
    public function getId(): int
    {
        return match ($this) {
            self::SEDAN => 1,
            self::STATION => 2,
            self::VAN => 3,
        };
    }
}
