<?php
namespace App\Enums;

enum PaymentStatus: string
{
    case OPEN = 'open';
    case CANCELED = 'canceled';
    case PENDING = 'pending';
    case AUTHORIZED = 'authorized';
    case EXPIRED = 'expired';
    case FAILED = 'failed';
    case PAID = 'paid';

    /**
     * Get the label for the enum value.
     *
     * @return string
     */
    public function label(): string
    {
        return match($this) {
            self::OPEN => 'Open',
            self::CANCELED => 'Canceled',
            self::PENDING => 'Pending',
            self::AUTHORIZED => 'Authorized',
            self::EXPIRED => 'Expired',
            self::FAILED => 'Failed',
            self::PAID => 'Paid',
        };
    }

    /**
     * Get all enum values as an array.
     *
     * @return array
     */
    public static function toArray(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all enum values with their labels as an array.
     *
     * @return array
     */
    public static function toSelectArray(): array
    {
        return collect(self::cases())->mapWithKeys(fn ($case) => [
            $case->value => $case->label()
        ])->all();
    }

    /**
     * Check if the payment is in a terminal state.
     *
     * @return bool
     */
    public function isTerminalState(): bool
    {
        return in_array($this, [
            self::PAID,
            self::FAILED,
            self::EXPIRED,
            self::CANCELED,
        ]);
    }

    /**
     * Check if the payment is in a successful state.
     *
     * @return bool
     */
    public function isSuccessful(): bool
    {
        return $this === self::PAID;
    }

    /**
     * Check if the payment requires further action.
     *
     * @return bool
     */
    public function requiresAction(): bool
    {
        return in_array($this, [
            self::OPEN,
            self::PENDING,
            self::AUTHORIZED,
        ]);
    }

    /**
     * Map Mollie webhook status to this enum.
     *
     * @param string $mollieStatus
     * @return self
     */
    public static function fromMollieStatus(string $mollieStatus): self
    {
        return match($mollieStatus) {
            'open' => self::OPEN,
            'canceled', 'cancelled' => self::CANCELED, // Mollie uses both spellings
            'pending' => self::PENDING,
            'authorized' => self::AUTHORIZED,
            'expired' => self::EXPIRED,
            'failed' => self::FAILED,
            'paid' => self::PAID,
            // 'confirmed' => self::PAID,
            default => throw new \InvalidArgumentException("Unknown Mollie status: {$mollieStatus}"),
        };
    }
}
