<?php

namespace App\Models;

use App\Enums\PaymentStatus;
use Illuminate\Database\Eloquent\Model;

/**
 * Model Payment
 *
 * @property PaymentStatus $status
 * @property-read \App\Models\Reservation|null $reservation
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment successful()
 *
 * @mixin \Eloquent
 */
class Payment extends Model
{

     /**
     * The data type of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    protected $fillable = [
        'id',
        'amount',
        'status',
        'paid_at',
        'reservation_id',
        'reservation_id',
        'metadata',
    ];

    protected $casts = [
        'id'    => 'string',
        'status' => PaymentStatus::class,
        'metadata' => 'array',
        'paid_at' => 'datetime',
    ];

    public function reservation()
    {
        return $this->belongsTo(Reservation::class, 'reservation_id', 'id');
    }

    /**
     * Scope a query to only include successful payments.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Check if the payment is successful.
     */
    public function isSuccessful()
    {
        return $this->status === PaymentStatus::PAID;
    }
}
