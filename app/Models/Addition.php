<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * 
 *
 * @method static Builder<static>|Addition active()
 * @method static Builder<static>|Addition newModelQuery()
 * @method static Builder<static>|Addition newQuery()
 * @method static Builder<static>|Addition query()
 * @mixin \Eloquent
 */
class Addition extends Model
{
    protected $guarded = ['id'];
    public $timestamps = false;

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('active', 1);
    }
}
