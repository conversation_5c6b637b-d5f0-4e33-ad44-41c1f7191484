<?php

namespace App\Models;

use App\Enums\ReservationStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\AsArrayObject;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 
 *
 * @property ReservationStatus $status
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Payment> $payments
 * @property-read int|null $payments_count
 * @property-write mixed $datetime
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reservation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reservation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reservation query()
 * @mixin \Eloquent
 */
class Reservation extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'data' => AsArrayObject::class,
        'status' => ReservationStatus::class,
    ];

    public function user(): BelongsTo
    {
        return $this->BelongsTo(User::class);
    }

    public static function generateRandId()
    {
        return strtoupper(substr(md5(microtime()), 0, 6));
    }

    public function setDatetimeAttribute($value)
    {
        $this->attributes['datetime'] = Carbon::parse($value);
    }

    /**
     * Update the reservation status.
     *
     * @param \App\Enums\ReservationStatus $status
     * @return bool
     */
    public function updateStatus(ReservationStatus $status): bool
    {
        if ($this->status->canTransitionTo($status)) {
            $this->status = $status;
            return $this->save();
        }

        return false;
    }

    /**
     * Get the payments for the reservation.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Check if the reservation has any successful payments.
     */
    public function isPaid(): bool
    {
        return $this->payments()->successful()->exists();
    }

    /**
     * Get the total amount paid for this reservation.
     */
    public function totalPaid()
    {
        return $this->payments()->successful()->sum('amount');
    }
}

/* USAGE EXAMPLES ReservationStatus:

The enum I've created includes:

All your specified statuses (NEW, PENDING, PENDING_PAYMENT, PAID, COMPLETED)
Several helpful methods:

label() - Returns a human-readable label (including "In Progress" for PENDING as you mentioned)
toArray() - Gets all possible values
toSelectArray() - Gets values with readable labels for dropdowns/selects
canTransitionTo() - Validates state transitions (e.g., NEW can only go to PENDING or PENDING_PAYMENT)

// Creating a new reservation
$reservation = Reservation::create([
    'user_id' => auth()->id(),
    'status' => ReservationStatus::NEW,
]);

// Or if you prefer using the string value
$reservation = Reservation::create([
    'user_id' => auth()->id(),
    'status' => ReservationStatus::NEW->value,
]);

// Updating status
$reservation->status = ReservationStatus::PENDING;
$reservation->save();

// Using the updateStatus method (with transition validation)
$reservation->updateStatus(ReservationStatus::PENDING);

// Checking current status
if ($reservation->status === ReservationStatus::PENDING_PAYMENT) {
    // Handle pending payment logic
}

// Getting the human-readable label
$statusLabel = $reservation->status->label(); // Returns "Pending Payment" for PENDING_PAYMENT

// In a Blade template
<span class="status-badge status-{{ $reservation->status->value }}">
    {{ $reservation->status->label() }}
</span>

// Getting all possible statuses for a dropdown
$statuses = ReservationStatus::toSelectArray();
// Returns ['new' => 'New', 'pending' => 'In Progress', ...]

// Using in a query
$pendingPaymentReservations = Reservation::where('status', ReservationStatus::PENDING_PAYMENT->value)->get();

*/
