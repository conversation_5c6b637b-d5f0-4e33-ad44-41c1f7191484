<?php

namespace App\Listeners;

use Illuminate\Support\Facades\Config;
use Stancl\Tenancy\Events\TenancyInitialized;
use Stancl\Tenancy\Resolvers\DomainTenantResolver;

class InitializeTenancy
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        Config::set('app.url', 'https://'.@DomainTenantResolver::$currentDomain->domain);

        // Not needed? idk. lookup what local_domain does..
        // Config::set('mail.mailers.smtp.local_domain', @DomainTenantResolver::$currentDomain->domain);
    }

    /**
     * Handle the event.
     */
    public function handle(TenancyInitialized $event): void
    {
        Config::set(
            'google-calendar.auth_profiles.service_account.credentials_json',
            storage_path('tenant/private/'.tenant('id').'_gc_creds.json') // /Users/<USER>/www/examp/storage/tenant/private/duru_gc_creds.json
        );

    }
}
