<?php

namespace App\Listeners;

use App\Events\ReservationCreated;
use App\Models\Reservation;
use Carbon\Carbon;
use Spatie\GoogleCalendar\Event;

class CreateCalendarEvent
{
    /**
     * Handle the event.
     */
    public function handle(ReservationCreated $event): void
    {
        if (tenant('google-calendar')['enabled']) {
            self::createEventFromReservation($event->reservation);
        }
    }

    public static function createEventFromReservation(Reservation $reservation)
    {

        // Google Calendar Event
        $event = new Event;
        $event->name = (! empty($reservation->data['waypoints']['origin']['route']) && ! empty($reservation->data['waypoints']['origin']['street_number']) ? $reservation->data['waypoints']['origin']['route'].' '.$reservation->data['waypoints']['origin']['street_number'] : $reservation->data['waypoints']['origin']['locality']);
        $event->name .= ' -> ';
        $event->name .= (! empty($reservation->data['waypoints']['destination']['route']) && ! empty($reservation->data['waypoints']['destination']['street_number']) ? $reservation->data['waypoints']['destination']['route'].' '.$reservation->data['waypoints']['destination']['street_number'].', '.$reservation->data['waypoints']['destination']['locality'] : $reservation->data['waypoints']['destination']['locality']);
        $event->location =
            $reservation->data['waypoints']['origin']['locality'] .', '.
            $reservation->data['waypoints']['origin']['route'] .' '.
            $reservation->data['waypoints']['origin']['street_number'];
        $event->startDateTime = new Carbon($reservation->datetime);
        $event->endDateTime = new Carbon($reservation->datetime);

        $event_description = view('notifications.gc.event-description', ['data' => $reservation->data, 'datetime' => $reservation->datetime, 'rand_id' => $reservation->rand_id])->render();

        // Remove double enters and double whitespaces
        $event_description = str($event_description)->replaceMatches('/(\r?\n){2,}/', PHP_EOL)->replaceMatches('/ {2,}/', ' ');

        $event->description = $event_description;
        $event->save();

        // save event id to reservation
        $reservation->data['google_calendar_event_id'] = $event->id;

        $reservation->save();
    }
}
