<?php

namespace App\Listeners;

use App\Events\ReservationCreated;
use Illuminate\Support\Facades\Mail;

class SendConfirmationToAdmin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ReservationCreated $event): void
    {
        Mail::to(config('mail.from.address'))
            ->send(new \App\Mail\NewReservation($event->reservation));
    }
}
