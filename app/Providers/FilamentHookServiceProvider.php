<?php

namespace App\Providers;

use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Illuminate\Contracts\View\View;

class FilamentHookServiceProvider extends ServiceProvider
{

    /**
     * Register services.
     */
    public function register(): void {}

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Not needed anymore
        // FilamentView::registerRenderHook(
        //     PanelsRenderHook::BODY_END, fn (): string => Blade::render('@livewire(\'wire-elements-modal\')'),
        // );

        // // Make flux useable: didnt work
        // FilamentView::registerRenderHook(
        //     PanelsRenderHook::BODY_END,
        //     fn (): string => Blade::render('@fluxScripts'),
        // );
    }
}
