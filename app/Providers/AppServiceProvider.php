<?php

namespace App\Providers;

use App\Livewire\ReservationWizardComponent;
use App\Livewire\Steps\FifthStepComponent;
use App\Livewire\Steps\FourthStepComponent;
use App\Livewire\Steps\FrontStepComponent;
use App\Livewire\Steps\SecondStepComponent;
use App\Livewire\Steps\ThirdStepComponent;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if (\App::environment('local')) {
            $loader = \Illuminate\Foundation\AliasLoader::getInstance();
            $loader->alias('Debugbar', \Barryvdh\Debugbar\Facades\Debugbar::class);
        }
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        Livewire::component('reservation-wizard', ReservationWizardComponent::class);
        Livewire::component('front', FrontStepComponent::class);
        Livewire::component('second', SecondStepComponent::class);
        Livewire::component('third', ThirdStepComponent::class);
        Livewire::component('fourth', FourthStepComponent::class);
        Livewire::component('fifth', FifthStepComponent::class);

        $this->configureCommands();
        $this->configureModels();
        $this->configureUrl();

    }

    /**
     * configureCommands
     */
    private function configureCommands()
    {
        DB::prohibitDestructiveCommands(
            app()->environment('production')
        );
    }

    /**
     * configureModels
     */
    private function configureModels()
    {
        Model::shouldBeStrict();
        Model::unguard();
        Model::automaticallyEagerLoadRelationships();
    }

    /**
     * configureUrl
     */
    private function configureUrl()
    {
        if (app()->environment('production')) {
            URL::forceHttps();
        }
    }
}
