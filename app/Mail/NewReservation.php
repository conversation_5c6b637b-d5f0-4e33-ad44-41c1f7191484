<?php

namespace App\Mail;

use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewReservation extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public Reservation $reservation
    ) {
        //
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Reservering '.tenant('settings')['sitename'],
            replyTo: [
                new Address($this->reservation->data['user']['email'], $this->reservation->data['user']['name']),
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $retour = null;
        if (! empty($this->reservation->data['retour_id'])) {
            $retour = Reservation::find($this->reservation->data['retour_id']);
        }

        return new Content(
            view: 'emails.confirmation-admin',
            with: [
                'retour' => $retour,
            ]
        );
    }
}
