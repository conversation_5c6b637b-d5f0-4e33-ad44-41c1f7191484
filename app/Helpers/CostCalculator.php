<?php

namespace App\Helpers;

use App\Models\Place;
use App\Models\Vehicle;
use Illuminate\Support\Facades\DB;

class CostCalculator
{
    // should we continue to calculate prices? or simply choose from list. Init:true
    private $shouldCalculatePrices = true;

    private $hasWaypoints = false;

    // count of waypoints other than origin/destination
    private $waypoint_count = 0;

    private $vehicles;

    protected $directions;

    protected $waypointData;

    /**
     * __construct
     *
     * @param  array  $directions
     * @param  array  $waypointData
     * @return void
     */
    public function __construct($directions, $waypointData)
    {

        $this->directions = $directions;

        $this->waypointData = $waypointData;

        $this->vehicles = Vehicle::active()->get()->keyBy('id');


        // If waypoints is enabled, && has waypoints (besides origin/destination)
        if (tenant('settings')['waypoints_enabled'] && count($this->waypointData) > 2) {

            $this->waypoint_count = count($this->waypointData) - 2;

            // if we have waypoints (other than origin-destination), load vehicles because we're gonna need it. // Else only load if calculation is needed.
            $this->hasWaypoints = true;

        }

    }

    /**
     * @return array<string, int>
     */
    public function calculate()
    {

        /*
         *
         * SHOULD CALCULATE?
         * We should calculate prices in some circumstances. These are:
         * - If ride has Waypoints
         * - If default prices don't exist
         *
         * PRIORITY
         * In one circumstance the defaultPrices have higher priority.
         * - If there are no waypoints.
         *
         * And in one circumstances the calculated prices have higher priority.
         * - If there are waypoints
         *
         */

        // If default (schiphol)prices exist; get them.
        $defaultPrices = $this->getDefaultPrices();

        /* We should calculate prices in some circumstances. These are:
        * - If default prices don't exist
         * - If ride has Waypoints
         */
        $this->shouldCalculatePrices = (
            $this->hasWaypoints ||
            empty($defaultPrices)
        );
        if ($this->shouldCalculatePrices) {
            $calculatedPrices = $this->getCalculatedPrices();
        }

        /* PRIORITY
         * In one circumstance the defaultPrices have higher priority.
         * - If there are no waypoints.
         *
         * And in one circumstances the calculated prices have higher priority.
         * - If there are waypoints
         */

        if ( !$this->hasWaypoints ) {

            // if defaultprices exist, they have higher prio because we dont have waypoints.
            // They could also not exist, if this isnt a schiphol ride.
            $prices = $defaultPrices ?? $calculatedPrices;

        } else {
            // if hasWaypoints must calculate. And compare calculated & (eventueel)default price and pick max.
            // and add the waypoint costs to total.

            foreach ($this->vehicles as $vehicle) {

                $prices[$vehicle->id]['subtotal'] = max(
                    ($defaultPrices[$vehicle->id]['subtotal'] ?? 0),
                    ($calculatedPrices[$vehicle->id]['subtotal'])
                );

                // add waypoints, and calculate totals
                $prices[$vehicle->id]['price_waypoints'] = $vehicle->price_waypoint * $this->waypoint_count;
                $prices[$vehicle->id]['subtotal'] = (int) $prices[$vehicle->id]['subtotal'] + $prices[$vehicle->id]['price_waypoints'];

            }

        }

        return $prices;

    }

    /**
     * getCalculatedPrices
     * Calculations are always compared to minimum price, and max() is taken.
     *
     * @return array|null
     */
    private function getCalculatedPrices()
    {

        $calculatedPrices = [];

        foreach ($this->vehicles as $vehicle) {

            $calculatedPrices[$vehicle->id] = $this->computePriceForVehicle(
                $vehicle,
                $this->directions['distance'],
                $this->directions['duration']
            );

            // put minprice in array for debugability.
            $calculatedPrices[$vehicle->id]['minimum'] = $vehicle->price_minimum;

            // check if minimum price is met, if not select minimumprice.
            $calculatedPrices[$vehicle->id]['subtotal'] = max($calculatedPrices[$vehicle->id]['calculated_total'], $vehicle->price_minimum);

        }

        return $calculatedPrices;

    }

    /**
     * computePriceForVehicle
     *
     * @param  object  $vehicle
     * @param  int  $distance
     * @param  int  $duration
     * @return array
     *               distance in km, duration in min
     */
    private function computePriceForVehicle(Vehicle $vehicle, $distance, $duration)
    {
        $price_start = $vehicle->price_start;
        $price_km = round($distance * $vehicle->price_km);
        $price_min = round($duration * $vehicle->price_min);

        $calculated_total = (int) round($price_start + $price_km + $price_min);

        return compact('distance', 'duration', 'price_start', 'price_km', 'price_min', 'calculated_total');

    }

    /**
     * getDefaultPrices if exists. Currently only working for schiphol.
     *
     * @return array|null
     */
    private function getDefaultPrices()
    {
        if ($schipholPoint = $this->isSchipholRide()) {

            // Opposite point to schiphol. Origin <> Destination
            $oppositePoint = $schipholPoint == 'origin' ? 'destination' : 'origin';

            // get the zipcode & locality of the opposite point
            $oppositeZipcode = substr($this->waypointData[$oppositePoint]['postal_code'], 0, 4);
            $oppositeLocality = $this->waypointData[$oppositePoint]['locality'];

            // Zipcode has higher priority than Locality.
            $defaultPricePlace = $this->getDefaultPricePlaceForZipcode($oppositeZipcode) ?? $this->getDefaultPricePlaceForLocality($oppositeLocality);

            // If a default price exists for this Zipcode/Locality (place); get all prices for this place for all Vehicles.
            if ($defaultPricePlace) {

                return $this->getDefaultPricesForPlaceAndVehicles($defaultPricePlace->id);

            }

        }

        return null;

    }

    /**
     * getDefaultPricePlaceForZipcode
     *
     * @param  string  $oppositeZipcode
     * @return object|null // model or null
     */
    private function getDefaultPricePlaceForZipcode($oppositeZipcode)
    {

        // check if opposite point is in default prices list
        // returns Model if found, else null
        $defaultPricePlace = Place::where(function ($query) use ($oppositeZipcode) {

            $query->where('type', 'zipcode')
                ->where('name', $oppositeZipcode);

        })->first(); // model or null

        // second check is for zipcoderange
        if (! $defaultPricePlace) {

            $defaultPricePlace = Place::where(function ($query) use ($oppositeZipcode) {

                $query->where('type', 'zipcoderange')
                    ->where('name', '<=', $oppositeZipcode)
                    ->where('zipcoderange_to', '>=', $oppositeZipcode);

            })->first(); // model or null

        }

        return $defaultPricePlace;

    }

    /**
     * getDefaultPricePlaceForLocality
     *
     * @param  string  $oppositeLocality
     * @return object|null // model or null
     */
    private function getDefaultPricePlaceForLocality($oppositeLocality)
    {

        return Place::where(function ($query) use ($oppositeLocality) {

            $query->where('type', 'place')
                ->where('name', $oppositeLocality);

        })->first(); // model or null

    }

    /**
     * getDefaultPricesForPlaceAndVehicles
     *
     * @param  int  $placeId
     * @return Collection
     */
    private function getDefaultPricesForPlaceAndVehicles($placeId)
    {
        return DB::table('place_vehicle')
            ->where('place_id', $placeId)
            ->selectRaw('vehicle_id, price as subtotal')
            ->get()
            ->keyBy('vehicle_id')
            ->map(fn ($item) => (array) $item)
            ->toArray();
    }

    /**
     * isSchipholRide
     *
     * @return bool
     */
    private function isSchipholRide()
    {
        if ($this->waypointData['origin']['locality'] == 'Schiphol') {

            return 'origin';

        } elseif ($this->waypointData['destination']['locality'] == 'Schiphol') {

            return 'destination';

        }

        return false;
    }
}
