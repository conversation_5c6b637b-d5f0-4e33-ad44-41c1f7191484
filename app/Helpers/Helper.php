<?php

namespace App\Helpers;

use RecursiveArrayIterator;
use RecursiveIteratorIterator;

class Helper
{
    public static function strfmon($int, $currency = 'EUR', $show_currency_symbol = true)
    {
        $currency_symbol = ($currency == 'EUR' ? '€' : ($currency == 'TRY' ? '₺' : ($currency == 'USD' ? '$' : '')));
        $int = str_pad($int, 3, '0', STR_PAD_LEFT);

        return ($show_currency_symbol ? $currency_symbol.' ' : '').substr_replace($int, ',', -2, 0);
    }

    public static function toNumerical($str)
    {
        return (int) preg_replace('/[^0-9]/', '', $str);
    }

    /*
    * Accepts geocoding response and returns the address components (with keys by subarray items).
    */
    public static function getComponentsFromResponse($response)
    {
        $response = $response->results[0];

        $components = [
            'place_id' => $response->place_id,
            'type' => $response->types[0],
        ];

        foreach ($response->address_components as $comp) {
            $components[$comp->types[0]] = $comp->long_name;
        }

        return $components;
    }

    /*
    * Accepts directions response and calculates cumulative distance & duration of legs between origin-waypoints-destination.
    */
    public static function computeTotalDistance($responseArray)
    {
        $route = $responseArray['routes'][0];
        $distance = $route['distanceMeters'] / 1000; // Convert meters to kilometers
        $duration = (int) $route['duration'] / 60; // Convert seconds to minutes

        return compact('distance', 'duration');
    }

    // int format to float
    // round($int/100,2);
    public static function intfflo($int)
    {
        return round($int / 100, 2);
    }

    public static function flatten_array_preserve_keys(array $array): array
    {
        $recursiveArrayIterator = new RecursiveArrayIterator(
            $array,
            RecursiveArrayIterator::CHILD_ARRAYS_ONLY
        );
        $iterator = new RecursiveIteratorIterator($recursiveArrayIterator);

        return iterator_to_array($iterator);
    }

    public static function formatAddressForEvent(array $address): string
    {
        if ($address['type'] == 'airport' || empty($address['route'])) {

            $address_str = $address['locality'];

        } else {

            $address_str = $address['route'].' '.($address['street_number'] ?? '').', '.$address['postal_code'].', '.$address['locality'];

        }

        return $address_str;
    }
}
