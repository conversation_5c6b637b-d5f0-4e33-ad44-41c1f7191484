<?php

namespace App\Helpers;

use GoogleMaps\Facade\GoogleMapsFacade as GoogleMaps;
use App\Helpers\Helper;

class GoogleMapsHelper
{

    public static function getPlace( $address, $withFormattedAddress = false )
    {
        $response = GoogleMaps::load('geocoding')
            ->setParam(['address' => $address])
            ->get();

            $response = json_decode($response);

        if ($response->status == 'OK'){

            $address_array = Helper::getComponentsFromResponse($response);
            if ($withFormattedAddress) $address_array['formatted_address'] = $response->results[0]->formatted_address;

            return $address_array;

        }

        return false;
    }

    public static function getDirections( $waypoints )
    {

        // IF waypoints are enabled && if there are waypoints.
        if( tenant('settings')['waypoints_enabled'] && count($waypoints) > 2 ) {

            $intermediates = collect($waypoints);

            // Remove origin/destination so that we are left with only the intermediate waypoints.
            $intermediates->forget('origin');
            $intermediates->forget('destination');

            $intermediates = $intermediates->pluck("value")->map(function ($waypoint) {
                return [
                    "address" => $waypoint,
                ];
            })->toArray();

        }

        $request_params = [
            "origin" => [ "address" => $waypoints['origin']['value'] ],
            "destination" => [ "address" => $waypoints['destination']['value'] ],
            "intermediates" => $intermediates ?? null,
            "regionCode" => "nl",
            "travelMode" => "Drive",
        ];

        /*
        * If we get the error: undefined array key "routes", there is a request/response error afaik..
        */
        $responseArray = GoogleMaps::load("routes")
            ->setParam($request_params)
            ->fetch();

        return Helper::computeTotalDistance($responseArray);
    }


}
