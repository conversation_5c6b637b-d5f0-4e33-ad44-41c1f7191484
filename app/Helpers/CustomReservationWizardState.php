<?php

namespace App\Helpers;

use <PERSON><PERSON>\LivewireWizard\Support\State;

class CustomReservationWizardState extends State
{
    public string $currentStepName;

    public function waypointData(): array
    {
        return $this->forStep('front')['waypoint_data'];
    }

    public function prices(): array
    {
        return $this->forStep('front')['prices'];
    }

    public function reservationData(): array
    {
        return [
            'datetime' => $this->forStep('front')['datetime'],
            'flightnr' => $this->forStep('front')['flightnr'],

            'retour' => $this->forStep('front')['retour'],
            'retour_datetime' => $this->forStep('front')['retour_datetime'],
            'retour_flightnr' => $this->forStep('front')['retour_flightnr'],
        ];
    }


    //method to reset state to current step.
    public function setStepState()
    {

        // Get the current step name using ComponentRegistry
        $currentStepName = $this->currentStepName;

        // Check if currentStepName is existant in the $stateData
        if( array_key_exists($currentStepName, $this->allState) ) {

            // This array will hold the steps up until the currentstep.
            $newStepsState = [];

            foreach ($this->allState as $key => $value) {

                $newStepsState[$key] = $value;

                if ($key == $currentStepName) break;

            }

            $this->allState =  $newStepsState;

        }

        // Update the session with the modified state
        session()->put('state', $this->allState);

    }

}
