<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
Use App\Enums\VehicleType;

class VehicleImage extends Component
{
    private $theme, $type;
    /**
     * Create a new component instance.
     */
    public function __construct( $theme, $type )
    {
        $this->theme = $theme;
        $this->type = $type;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.vehicle-themes.' . $this->theme .'.'. $this->type);
    }
}
