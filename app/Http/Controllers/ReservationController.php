<?php

namespace App\Http\Controllers;

use App\Enums\PaymentStatus;
use App\Models\Reservation;
use Illuminate\Http\Request;
use Illuminate\Support\Sleep;
use Illuminate\Support\Facades\Log;

class ReservationController extends Controller
{

    protected $reservation, $payment;


    /**
     * __construct
     */
    public function __construct(Request $request)
    {

        $reservation_rand_id = $request->route('reservation_rand_id');

        // Get reservation from parameter if set, otherwise from session.
        $this->reservation = Reservation::where('rand_id', $reservation_rand_id)->firstOrFail();

        $this->payment = $this->reservation->payments()->latest()->first();

    }

    public function show()
    {
        // If offline payment, then success.
        if($this->reservation->data['payment_method'] !== 'mollie'){

            // reset state after reservation COMPLETE.
            session()->forget('state');
            session()->forget('reservationId');

            return view('reservation', [
                'status' => 'success',
                'rand_id' => $this->reservation->rand_id,
                'title' => 'Succesvol gereserveerd!',
                'message' => 'Reservering <b>#'.$this->reservation->rand_id.'</b> is succesvol geplaatst.<br>Bedankt dat u voor ons hebt gekozen!<br> Nog een fijne dag verder en tot ziens!',
            ]);

        }

        // If mollie & paid OR payment method is not mollie (null (no payment method), cash, pin): success
        elseif($this->reservation->data['payment_method'] === 'mollie'){

            // The redirect is always faster than the webhook update. So in first pageload it always said payment not yet completed.
            // Hopefully this 1 second gives the mollie webhook etc. the change to update the payment and give a success! message to the customer.
            Sleep::for(2)->seconds();

            // If Payment Successful
            if($this->payment->status === PaymentStatus::PAID){

                // reset state after reservation COMPLETE.
                session()->forget('state');
                session()->forget('reservationId');

                return view('reservation', [
                    'status' => 'success',
                    'rand_id' => $this->reservation->rand_id,
                    'title' => 'Succesvol gereserveerd!',
                    'message' => 'Reservering <b>#'.$this->reservation->rand_id.'</b> is succesvol geplaatst.<br>Bedankt dat u voor ons hebt gekozen!<br> Nog een fijne dag verder en tot ziens!',
                ]);

            // If Payment Not
            }else{

                // Check if there is an error message in the session
                if (session()->has('error')) {
                    $status = 'failed';
                    $title = 'Betaling mislukt';
                    $message = session('error');
                }

                // Refactoring needed: If payment is terminated (failed/cancelled) then delete reservation(s) from db.
                // Else if payment is pending, then show pending view. And show link with click here to recheck.
                // Maybe add a retry payment link. Which redirects to Molliecheckoutcontroller.

                return view('reservation', [
                    'status' => $status ?? $this->payment->status,
                    'rand_id' => $this->reservation->rand_id,
                    'title' => $title ?? 'Betaling niet voltooid',
                    'message' => $message ?? 'Uw betaling is nog niet verwerkt. We zullen uw reservering bijwerken zodra de betaling is bevestigd.',
                ]);

            }

        }
    }
}
