<?php

namespace App\Http\Controllers;

use App\Enums\PaymentStatus;
use App\Enums\ReservationStatus;
use App\Models\Payment;
use App\Models\Reservation;
use Illuminate\Http\Request;
use Mollie\Laravel\Facades\Mollie;
use Illuminate\Support\Facades\Log;

class MolliePaymentController extends Controller
{
    /**
     * Show the payment form.
     */
    public static function checkout( $reservationId )
    {

        $reservation = Reservation::findOrFail( $reservationId );

        try {
            // Set Mollie API key from tenantData.
            Mollie::api()->setApiKey(tenant('mollie')['key']);

            // Create payment
            $molliePayment = Mollie::api()->payments->create([
                'amount' => [
                    'currency' => 'EUR',
                    'value' => number_format($reservation->data['price'] / 100, 2, '.', ''),
                ],
                'description' => 'Reservation '.$reservation->rand_id,
                'redirectUrl' => route('reservation', ['reservation_rand_id' => $reservation->rand_id]),
                'webhookUrl' => route('webhooks.mollie'),
                'metadata' => [
                    'reservation_id' => $reservation->id,
                    'reservation_rand_id' => $reservation->rand_id,
                ],
            ]);

            $payment = Payment::create([
                'id' => $molliePayment->id,
                'reservation_id' => $reservation->id,
                'amount' => (int) $reservation->data['price'],
                'status' => PaymentStatus::PENDING,
                'metadata' => [
                    'mollie_data' => json_decode(json_encode($molliePayment), true),
                ],
            ]);

            // Redirect to Mollie checkout
            return redirect($molliePayment->getCheckoutUrl(), 303);

        } catch (\Exception $e) {
            Log::error('Mollie payment error: '.$e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['payment_error' => 'Payment initiation failed: '.$e->getMessage()]);
        }

    }

    /**
     * Handle Mollie webhook notifications.
     */
    public function handleWebhook(Request $request)
    {
        Log::info('Mollie WebHook received at:' . now()->toIso8601String());
        if ( !$request->has('id')) return;

        try {
            $paymentId = $request->input('id');

            Mollie::api()->setApiKey(tenant('mollie')['key']);

            // Fetch payment from Mollie
            $molliePayment = Mollie::api()->payments->get($paymentId);

            // Find the payment in our database
            $payment = Payment::find($paymentId);

            // If payment not found in our database
            if ( !$payment ) {

                Log::error('Mollie webhook error: Payment not found in database. Payment ID: '.$paymentId);

                return response('Payment not found', 404);
            }

            $payment->update([
                'status'    => PaymentStatus::fromMollieStatus($molliePayment->status),
                'metadata'  => array_merge($payment->metadata ?? [], [
                    'mollie_webhook_data' => json_decode(json_encode($molliePayment), true),
                    'webhook_received_at' => now()->toIso8601String(),
                ])
            ]);

            if($molliePayment->isPaid()){

                // Find the reservation and update from 'temporary' to 'new'
                $reservation = Reservation::find($payment->reservation_id);
                $reservation->update([
                    'status' => ReservationStatus::NEW,
                    'data->payment_status' => 'paid',
                    'data->payment_id' => $payment->id,
                ]);

                // If has retour get retour-reservation and update that aswell.
                if(isset($reservation->data['retour_id'])){

                    Reservation::where('id', $reservation->data['retour_id'])
                    ->update([
                        'status' => ReservationStatus::NEW,
                        'data->payment_status' => 'paid',
                        'data->payment_id' => $payment->id,
                    ]);

                }

            }

            return response('Webhook processed', 200);

        } catch (\Exception $e) {
            // Log the error
            \Log::error('Mollie webhook error: '.$e->getMessage());

            return response('Webhook processing failed: '.$e->getMessage(), 500);
        }
    }
}
