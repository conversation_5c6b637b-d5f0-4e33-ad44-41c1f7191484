<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('111111'),

        ]);

        Artisan::call('tenants:migrate-fresh');
        Artisan::call('tenants:seed');
    }
}
