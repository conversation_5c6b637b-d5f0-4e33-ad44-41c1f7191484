<?php

namespace Database\Seeders\Tenant;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class TenantDatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        $temporary_password = Hash::make(Str::random(16));

        \App\Models\User::updateOrCreate(
            ['name' => tenant()->id,
            'email' => /* tenant()->mail['from_address'] ?? */ tenant()->id.'@ikboek.nl'],
            ['role' => 'admin',
            'password' => '123456',//$temporary_password,
            ]
        );

    }
}
