<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\ReservationStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reservations', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string('rand_id', length: 6);
            $table->foreignId('user_id')->nullable()->constraint();
            $table->datetime('datetime');
            // $table->string('status', length: 12)->default('NEW');
            $table->string('status', length: 12)->default(ReservationStatus::NEW->value);

            $table->json('data')->comment('
            (array) origin, OR if is reservation of user: origin_id=address,
            (array) ?waypoint, OR if is reservation of user: origin_id=address, ...
            (array) destination, OR if is reservation of user: destination_id=address,

            (int) vehicle_id,
            (varchar 10) flightnr,
            (tinyInt) people,
            (str) payment_method,
            (tinyInt) luggage,
            (tinyInt) handluggage,
            (str) comment,
            (int) price,
            (int) distance (in km),
            (int) duration (min),
            (str) google_calendar_event_id,
            (tinyInt) retour_id, reservation_id of retourreservation if this is a reservation with a retour.,
            (bool) is_retour, if this is the retourreservation of a reservation.

            ');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reservations');
    }
};
