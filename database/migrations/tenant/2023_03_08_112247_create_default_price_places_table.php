<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('default_price_places', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string('name');
            $table->string('type')->comment('place/zipcode/zipcoderange eg: "Haarlem" OR "2022" OR "1361-1366" (eg: all zipcodes between 1361-1366 INCLUSIVE)');
            $table->integer('zipcoderange_to')->nullable();
            // The zipcoderange_to is only used for zipcoderange type.
            // Using separate column because looking it up otherwise is a costly query.
            // This way we can just use two where's ona query, and its way faster.
        });

        DB::table('default_price_places')->insert([
            ['id' => 1, 'name' => 'Aerdenhout', 'type' => 'place'],
            ['id' => 2, 'name' => 'Alblasserdam', 'type' => 'place'],
            ['id' => 3, 'name' => 'Alkmaar', 'type' => 'place'],
            ['id' => 4, 'name' => 'Amersfoort', 'type' => 'place'],
            ['id' => 5, 'name' => 'Amsterdam-Noord', 'type' => 'place'],
            ['id' => 6, 'name' => 'Amsterdam-Oost', 'type' => 'place'],
            ['id' => 7, 'name' => 'Amsterdam-West', 'type' => 'place'],
            ['id' => 8, 'name' => 'Amsterdam-Zuid', 'type' => 'place'],
            ['id' => 9, 'name' => 'Amsterdam-Zuidoost', 'type' => 'place'],
            ['id' => 10, 'name' => 'Apeldoorn', 'type' => 'place'],
            ['id' => 11, 'name' => 'Assendelft', 'type' => 'place'],
            ['id' => 12, 'name' => 'Baarn', 'type' => 'place'],
            ['id' => 13, 'name' => 'Barendrecht', 'type' => 'place'],
            ['id' => 14, 'name' => 'Barneveld', 'type' => 'place'],
            ['id' => 15, 'name' => 'Beemster', 'type' => 'place'],
            ['id' => 16, 'name' => 'Bennebroek', 'type' => 'place'],
            ['id' => 17, 'name' => 'Bentveld', 'type' => 'place'],
            ['id' => 18, 'name' => 'Bergen', 'type' => 'place'],
            ['id' => 19, 'name' => 'Berkel en Rodenrijs', 'type' => 'place'],
            ['id' => 20, 'name' => 'Beverwijk', 'type' => 'place'],
            ['id' => 21, 'name' => 'Blaricum', 'type' => 'place'],
            ['id' => 22, 'name' => 'Bloemendaal', 'type' => 'place'],
            ['id' => 23, 'name' => 'Bodegraven', 'type' => 'place'],
            ['id' => 24, 'name' => 'Bovenkarspel', 'type' => 'place'],
            ['id' => 25, 'name' => 'Castricum', 'type' => 'place'],
            ['id' => 26, 'name' => 'Cruquius', 'type' => 'place'],
            ['id' => 27, 'name' => 'De Kwakel', 'type' => 'place'],
            ['id' => 28, 'name' => 'Delft', 'type' => 'place'],
            ['id' => 29, 'name' => 'Den Helder', 'type' => 'place'],
            ['id' => 30, 'name' => 'Diemen', 'type' => 'place'],
            ['id' => 31, 'name' => 'Enkhuizen', 'type' => 'place'],
            ['id' => 32, 'name' => 'Gouda', 'type' => 'place'],
            ['id' => 33, 'name' => 'Haarlem', 'type' => 'place'],
            ['id' => 34, 'name' => 'Halfweg', 'type' => 'place'],
            ['id' => 35, 'name' => 'Heemskerk', 'type' => 'place'],
            ['id' => 36, 'name' => 'Heemstede', 'type' => 'place'],
            ['id' => 37, 'name' => 'Heerhugowaard', 'type' => 'place'],
            ['id' => 38, 'name' => 'Heiloo', 'type' => 'place'],
            ['id' => 39, 'name' => 'Hillegom', 'type' => 'place'],
            ['id' => 40, 'name' => 'Hoofddorp', 'type' => 'place'],
            ['id' => 41, 'name' => 'Hoogkarspel', 'type' => 'place'],
            ['id' => 42, 'name' => 'Hoorn', 'type' => 'place'],
            ['id' => 43, 'name' => 'Houten', 'type' => 'place'],
            ['id' => 44, 'name' => 'IJmuiden', 'type' => 'place'],
            ['id' => 45, 'name' => 'IJsselstein', 'type' => 'place'],
            ['id' => 46, 'name' => 'Katwijk', 'type' => 'place'],
            ['id' => 47, 'name' => 'Krommenie', 'type' => 'place'],
            ['id' => 48, 'name' => 'Leiden', 'type' => 'place'],
            ['id' => 49, 'name' => 'Leiderdorp', 'type' => 'place'],
            ['id' => 50, 'name' => 'Leidschendam', 'type' => 'place'],
            ['id' => 51, 'name' => 'Leimuiden', 'type' => 'place'],
            ['id' => 52, 'name' => 'Lelystad', 'type' => 'place'],
            ['id' => 53, 'name' => 'Lisse', 'type' => 'place'],
            ['id' => 54, 'name' => 'Naaldwijk', 'type' => 'place'],
            ['id' => 55, 'name' => 'Nieuwegein', 'type' => 'place'],
            ['id' => 56, 'name' => 'Nieuwerkerk Ijssel', 'type' => 'place'],
            ['id' => 57, 'name' => 'Nijkerk', 'type' => 'place'],
            ['id' => 58, 'name' => 'Noordwijk', 'type' => 'place'],
            ['id' => 59, 'name' => 'Noordwijkerhout', 'type' => 'place'],
            ['id' => 60, 'name' => 'Oegstgeest', 'type' => 'place'],
            ['id' => 61, 'name' => 'Overveen', 'type' => 'place'],
            ['id' => 62, 'name' => 'Purmerend', 'type' => 'place'],
            ['id' => 63, 'name' => 'Rijsenhout', 'type' => 'place'],
            ['id' => 64, 'name' => 'Rijswijk', 'type' => 'place'],
            ['id' => 65, 'name' => 'Rotterdam', 'type' => 'place'],
            ['id' => 66, 'name' => 'Santpoort-Noord', 'type' => 'place'],
            ['id' => 67, 'name' => 'Santpoort-Zuid', 'type' => 'place'],
            ['id' => 68, 'name' => 'Sassenheim', 'type' => 'place'],
            ['id' => 69, 'name' => 'Schagen', 'type' => 'place'],
            ['id' => 70, 'name' => 'Schiedam', 'type' => 'place'],
            ['id' => 71, 'name' => 'Sliedrecht', 'type' => 'place'],
            ['id' => 72, 'name' => 'Spaarndam', 'type' => 'place'],
            ['id' => 73, 'name' => 'Ter Aar', 'type' => 'place'],
            ['id' => 74, 'name' => 'Uitgeest', 'type' => 'place'],
            ['id' => 75, 'name' => 'Utrecht', 'type' => 'place'],
            ['id' => 76, 'name' => 'Velsen-Noord', 'type' => 'place'],
            ['id' => 77, 'name' => 'Velsen-Zuid', 'type' => 'place'],
            ['id' => 78, 'name' => 'Velserbroek', 'type' => 'place'],
            ['id' => 79, 'name' => 'Vijfhuizen', 'type' => 'place'],
            ['id' => 80, 'name' => 'Vlaardingen', 'type' => 'place'],
            ['id' => 81, 'name' => 'Volendam', 'type' => 'place'],
            ['id' => 82, 'name' => 'Voorburg', 'type' => 'place'],
            ['id' => 83, 'name' => 'Voorhout', 'type' => 'place'],
            ['id' => 84, 'name' => 'Voorschoten', 'type' => 'place'],
            ['id' => 85, 'name' => 'Waddinxveen', 'type' => 'place'],
            ['id' => 86, 'name' => 'Wassenaar', 'type' => 'place'],
            ['id' => 87, 'name' => 'Zaandam', 'type' => 'place'],
            ['id' => 88, 'name' => 'Zandvoort', 'type' => 'place'],
            ['id' => 89, 'name' => 'Zoetermeer', 'type' => 'place'],
            ['id' => 90, 'name' => 'Zoeterwoude', 'type' => 'place'],
            ['id' => 91, 'name' => 'Zwijndrecht', 'type' => 'place'],
            ['id' => 92, 'name' => 'Aalsmeer', 'type' => 'place'],
            ['id' => 93, 'name' => 'Abcoude', 'type' => 'place'],
            ['id' => 94, 'name' => 'Almere-Buiten', 'type' => 'place'],
            ['id' => 95, 'name' => 'Almere-Hout', 'type' => 'place'],
            ['id' => 96, 'name' => 'Almere', 'type' => 'place'],
            ['id' => 97, 'name' => 'Alphen aan den Rijn', 'type' => 'place'],
            ['id' => 98, 'name' => 'Amstelhoek', 'type' => 'place'],
            ['id' => 99, 'name' => 'Amstelveen', 'type' => 'place'],
            ['id' => 100, 'name' => 'Badhoevedorp', 'type' => 'place'],
            ['id' => 101, 'name' => 'Bilthoven', 'type' => 'place'],
            ['id' => 102, 'name' => 'Breukelen', 'type' => 'place'],
            ['id' => 103, 'name' => 'Bussum', 'type' => 'place'],
            ['id' => 104, 'name' => 'Eemnes', 'type' => 'place'],
            ['id' => 105, 'name' => 'Hilversum', 'type' => 'place'],
            ['id' => 106, 'name' => 'Huizen', 'type' => 'place'],
            ['id' => 107, 'name' => 'Laren', 'type' => 'place'],
            ['id' => 108, 'name' => 'Maarssen', 'type' => 'place'],
            ['id' => 109, 'name' => 'Mijdrecht', 'type' => 'place'],
            ['id' => 110, 'name' => 'Monnickendam', 'type' => 'place'],
            ['id' => 111, 'name' => 'Muiden', 'type' => 'place'],
            ['id' => 112, 'name' => 'Naarden', 'type' => 'place'],
            ['id' => 113, 'name' => 'Nieuw-Vennep', 'type' => 'place'],
            ['id' => 114, 'name' => 'Nieuwkoop', 'type' => 'place'],
            ['id' => 115, 'name' => 'Ouderkerk aan de Amstel', 'type' => 'place'],
            ['id' => 116, 'name' => 'Schiphol-Rijk', 'type' => 'place'],
            ['id' => 117, 'name' => 'Soest', 'type' => 'place'],
            ['id' => 118, 'name' => 'Uithoorn', 'type' => 'place'],
            ['id' => 119, 'name' => 'Vinkeveen', 'type' => 'place'],
            ['id' => 120, 'name' => 'Weesp', 'type' => 'place'],
            ['id' => 121, 'name' => 'Wilnis', 'type' => 'place'],
            ['id' => 122, 'name' => 'Woerden', 'type' => 'place'],
            ['id' => 123, 'name' => 'Almere poort', 'type' => 'place'],
            ['id' => 124, 'name' => 'Ankeveen', 'type' => 'place'],
            ['id' => 125, 'name' => 'Baambrugge', 'type' => 'place'],
            ['id' => 126, 'name' => 'Kockengen', 'type' => 'place'],
            ['id' => 127, 'name' => 'Kortenhoef', 'type' => 'place'],
            ['id' => 128, 'name' => 'Kudelstaart', 'type' => 'place'],
            ['id' => 129, 'name' => 'Loenen aan de vecht', 'type' => 'place'],
            ['id' => 130, 'name' => 'Loosdrecht', 'type' => 'place'],
            ['id' => 131, 'name' => 'Nederhorst den berg', 'type' => 'place'],
            ['id' => 132, 'name' => 'Nieuwer ter aan', 'type' => 'place'],
            ['id' => 133, 'name' => '\'s-Graveland', 'type' => 'place'],
            ['id' => 134, 'name' => 'Vreeland', 'type' => 'place'],

            ['id' => 135, 'name' => '1380', 'type' => 'zipcode'],
            ['id' => 136, 'name' => '1381', 'type' => 'zipcode'],
            ['id' => 137, 'name' => '1382', 'type' => 'zipcode'],
            ['id' => 138, 'name' => '1383', 'type' => 'zipcode'],
            ['id' => 139, 'name' => '1384', 'type' => 'zipcode'],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('default_price_places');
    }
};
