<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\VehicleType;
use Illuminate\Support\Facades\DB;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();

            $table->boolean('active')->default(1);

            $table->string('type', 15)->comment('Type=enum: sedan, station, van');

            $table->string('name');

            $table->unsignedInteger('price_start')->default(0);
            $table->unsignedInteger('price_km')->default(0);
            $table->unsignedInteger('price_min')->default(0);
            $table->unsignedInteger('price_waypoint')->comment('for every waypoint, extra costs');

            $table->unsignedInteger('price_minimum')->comment('Minimum totalprice for a ride with this vehicle');

            $table->unsignedInteger('max_people');
            $table->unsignedInteger('max_luggage');
            $table->unsignedInteger('max_handluggage');
            $table->unsignedInteger('max_combined_luggage')->comment('if filled in, the combined number of luggage+handluggage should be below this nr.')->nullable();

            $table->string('label_1')->nullable();
            $table->string('label_2')->nullable();
            $table->string('label_3')->nullable();
            $table->string('label_4')->nullable();
        });

        // Default Data
        $vehicles = [
            [
                'id' => 1,
                'type' => VehicleType::SEDAN,
                'name' => 'Sedan',
                'price_km' => 140,
                'price_min' => 40,
                'price_start' => 339,
                'price_waypoint' => 750,
                'price_minimum' => 3400,
                'max_people' => 4,
                'max_luggage' => 4,
                'max_handluggage' => 4,
                'max_combined_luggage' => 6,
                'label_1' => 'tot 4 personen',
                'label_2' => 'Altijd een privé-taxi',
                'label_3' => 'Bagage gratis mee',
                'label_4' => 'Luxe & Comfort',
            ],
            [
                'id' => 2,
                'type' => VehicleType::STATION,
                'name' => 'Stationcar',
                'price_km' => 140,
                'price_min' => 40,
                'price_start' => 339,
                'price_waypoint' => 750,
                'price_minimum' => 3400,
                'max_people' => 4,
                'max_luggage' => 4,
                'max_handluggage' => 4,
                'max_combined_luggage' => 6,
                'label_1' => 'tot 4 personen',
                'label_2' => 'Altijd een privé-taxi',
                'label_3' => 'Bagage gratis mee',
                'label_4' => 'Luxe & Comfort',
            ],
            [
                'id' => 3,
                'type' => VehicleType::VAN,
                'name' => 'Bus',
                'price_km' => 180,
                'price_min' => 45,
                'price_start' => 339,
                'price_waypoint' => 1000,
                'price_minimum' => 4500,
                'max_people' => 7,
                'max_luggage' => 6,
                'max_handluggage' => 6,
                'max_combined_luggage' => 10,
                'label_1' => 'tot 7 personen',
                'label_2' => 'Altijd een privé-taxi',
                'label_3' => 'Bagage gratis mee',
                'label_4' => 'Luxe & Comfort',
            ]
        ];

        foreach($vehicles as $vehicle){

            DB::table('vehicles')->insert($vehicle);

        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
