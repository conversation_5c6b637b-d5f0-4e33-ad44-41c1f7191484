<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('additions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->unsignedMediumInteger('price');
            $table->string('icon')->nullable();
            $table->boolean('active')->default(1);
        });

        $additions = [
            [ 'name' => 'Kinderzitje', 'description' => null, 'price' => 1000, 'active' => 0],
            [ 'name' => 'Pet/Huisdier', 'description' => null, 'price' => 1000, 'active' => 0],
        ];

        foreach($additions as $addition){

            DB::table('additions')->insert($addition);

        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('additions');
    }
};
