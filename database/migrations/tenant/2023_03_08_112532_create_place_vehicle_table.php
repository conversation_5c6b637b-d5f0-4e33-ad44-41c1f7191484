<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('place_vehicle', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->foreignId('place_id');
            $table->foreignId('vehicle_id');
            $table->unsignedInteger('price')->comment('default price for chosen vehicle, for chosen place.');
        });

        // stationcar
        DB::table('place_vehicle')->insert(
            [
                ['place_id' => 1, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 2, 'vehicle_id' => 1, 'price' => 10000],
                ['place_id' => 3, 'vehicle_id' => 1, 'price' => 7500],
                ['place_id' => 4, 'vehicle_id' => 1, 'price' => 9000],
                ['place_id' => 5, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 6, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 7, 'vehicle_id' => 1, 'price' => 3400],
                ['place_id' => 8, 'vehicle_id' => 1, 'price' => 3400],
                ['place_id' => 9, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 10, 'vehicle_id' => 1, 'price' => 14400],
                ['place_id' => 11, 'vehicle_id' => 1, 'price' => 5900],
                ['place_id' => 12, 'vehicle_id' => 1, 'price' => 8000],
                ['place_id' => 13, 'vehicle_id' => 1, 'price' => 11500],
                ['place_id' => 14, 'vehicle_id' => 1, 'price' => 10000],
                ['place_id' => 15, 'vehicle_id' => 1, 'price' => 7500],
                ['place_id' => 16, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 17, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 18, 'vehicle_id' => 1, 'price' => 8000],
                ['place_id' => 19, 'vehicle_id' => 1, 'price' => 8000],
                ['place_id' => 20, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 21, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 22, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 23, 'vehicle_id' => 1, 'price' => 6900],
                ['place_id' => 24, 'vehicle_id' => 1, 'price' => 10900],
                ['place_id' => 25, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 26, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 27, 'vehicle_id' => 1, 'price' => 4000],
                ['place_id' => 28, 'vehicle_id' => 1, 'price' => 8000],
                ['place_id' => 29, 'vehicle_id' => 1, 'price' => 14000],
                ['place_id' => 30, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 31, 'vehicle_id' => 1, 'price' => 10900],
                ['place_id' => 32, 'vehicle_id' => 1, 'price' => 8500],
                ['place_id' => 33, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 34, 'vehicle_id' => 1, 'price' => 4000],
                ['place_id' => 35, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 36, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 37, 'vehicle_id' => 1, 'price' => 8000],
                ['place_id' => 38, 'vehicle_id' => 1, 'price' => 7500],
                ['place_id' => 39, 'vehicle_id' => 1, 'price' => 4900],
                ['place_id' => 40, 'vehicle_id' => 1, 'price' => 3900],
                ['place_id' => 41, 'vehicle_id' => 1, 'price' => 10900],
                ['place_id' => 42, 'vehicle_id' => 1, 'price' => 8500],
                ['place_id' => 43, 'vehicle_id' => 1, 'price' => 9000],
                ['place_id' => 44, 'vehicle_id' => 1, 'price' => 5400],
                ['place_id' => 45, 'vehicle_id' => 1, 'price' => 7900],
                ['place_id' => 46, 'vehicle_id' => 1, 'price' => 5900],
                ['place_id' => 47, 'vehicle_id' => 1, 'price' => 6400],
                ['place_id' => 48, 'vehicle_id' => 1, 'price' => 6900],
                ['place_id' => 49, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 50, 'vehicle_id' => 1, 'price' => 6900],
                ['place_id' => 51, 'vehicle_id' => 1, 'price' => 4000],
                ['place_id' => 52, 'vehicle_id' => 1, 'price' => 10900],
                ['place_id' => 53, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 54, 'vehicle_id' => 1, 'price' => 8400],
                ['place_id' => 55, 'vehicle_id' => 1, 'price' => 7900],
                ['place_id' => 56, 'vehicle_id' => 1, 'price' => 9000],
                ['place_id' => 57, 'vehicle_id' => 1, 'price' => 10900],
                ['place_id' => 58, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 59, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 60, 'vehicle_id' => 1, 'price' => 5400],
                ['place_id' => 61, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 62, 'vehicle_id' => 1, 'price' => 6400],
                ['place_id' => 63, 'vehicle_id' => 1, 'price' => 3400],
                ['place_id' => 64, 'vehicle_id' => 1, 'price' => 8900],
                ['place_id' => 65, 'vehicle_id' => 1, 'price' => 10400],
                ['place_id' => 66, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 67, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 68, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 69, 'vehicle_id' => 1, 'price' => 9500],
                ['place_id' => 70, 'vehicle_id' => 1, 'price' => 9500],
                ['place_id' => 71, 'vehicle_id' => 1, 'price' => 13000],
                ['place_id' => 72, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 73, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 74, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 75, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 76, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 77, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 78, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 79, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 80, 'vehicle_id' => 1, 'price' => 9500],
                ['place_id' => 81, 'vehicle_id' => 1, 'price' => 7500],
                ['place_id' => 82, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 83, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 84, 'vehicle_id' => 1, 'price' => 6900],
                ['place_id' => 85, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 86, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 87, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 88, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 89, 'vehicle_id' => 1, 'price' => 7500],
                ['place_id' => 90, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 91, 'vehicle_id' => 1, 'price' => 11500],
                ['place_id' => 92, 'vehicle_id' => 1, 'price' => 3400],
                ['place_id' => 93, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 94, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 95, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 96, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 97, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 98, 'vehicle_id' => 1, 'price' => 4000],
                ['place_id' => 99, 'vehicle_id' => 1, 'price' => 3400],
                ['place_id' => 100, 'vehicle_id' => 1, 'price' => 3500],
                ['place_id' => 101, 'vehicle_id' => 1, 'price' => 8400],
                ['place_id' => 102, 'vehicle_id' => 1, 'price' => 5400],
                ['place_id' => 103, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 104, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 105, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 106, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 107, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 108, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 109, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 110, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 111, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 112, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 113, 'vehicle_id' => 1, 'price' => 4900],
                ['place_id' => 114, 'vehicle_id' => 1, 'price' => 5400],
                ['place_id' => 115, 'vehicle_id' => 1, 'price' => 3900],
                ['place_id' => 116, 'vehicle_id' => 1, 'price' => 3500],
                ['place_id' => 117, 'vehicle_id' => 1, 'price' => 7500],
                ['place_id' => 118, 'vehicle_id' => 1, 'price' => 4500],
                ['place_id' => 119, 'vehicle_id' => 1, 'price' => 5000],
                ['place_id' => 120, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 121, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 122, 'vehicle_id' => 1, 'price' => 7000],
                ['place_id' => 123, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 124, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 125, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 126, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 127, 'vehicle_id' => 1, 'price' => 6000],
                ['place_id' => 128, 'vehicle_id' => 1, 'price' => 4000],
                ['place_id' => 129, 'vehicle_id' => 1, 'price' => 5400],
                ['place_id' => 130, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 131, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 132, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 133, 'vehicle_id' => 1, 'price' => 6500],
                ['place_id' => 134, 'vehicle_id' => 1, 'price' => 5400],

                ['place_id' => 135, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 136, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 137, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 138, 'vehicle_id' => 1, 'price' => 5500],
                ['place_id' => 139, 'vehicle_id' => 1, 'price' => 5500],
            ]
        );

        // stationcar
        DB::table('place_vehicle')->insert(
            [
                ['place_id' => 1, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 2, 'vehicle_id' => 2, 'price' => 10000],
                ['place_id' => 3, 'vehicle_id' => 2, 'price' => 7500],
                ['place_id' => 4, 'vehicle_id' => 2, 'price' => 9000],
                ['place_id' => 5, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 6, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 7, 'vehicle_id' => 2, 'price' => 3400],
                ['place_id' => 8, 'vehicle_id' => 2, 'price' => 3400],
                ['place_id' => 9, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 10, 'vehicle_id' => 2, 'price' => 14400],
                ['place_id' => 11, 'vehicle_id' => 2, 'price' => 5900],
                ['place_id' => 12, 'vehicle_id' => 2, 'price' => 8000],
                ['place_id' => 13, 'vehicle_id' => 2, 'price' => 11500],
                ['place_id' => 14, 'vehicle_id' => 2, 'price' => 10000],
                ['place_id' => 15, 'vehicle_id' => 2, 'price' => 7500],
                ['place_id' => 16, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 17, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 18, 'vehicle_id' => 2, 'price' => 8000],
                ['place_id' => 19, 'vehicle_id' => 2, 'price' => 8000],
                ['place_id' => 20, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 21, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 22, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 23, 'vehicle_id' => 2, 'price' => 6900],
                ['place_id' => 24, 'vehicle_id' => 2, 'price' => 10900],
                ['place_id' => 25, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 26, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 27, 'vehicle_id' => 2, 'price' => 4000],
                ['place_id' => 28, 'vehicle_id' => 2, 'price' => 8000],
                ['place_id' => 29, 'vehicle_id' => 2, 'price' => 14000],
                ['place_id' => 30, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 31, 'vehicle_id' => 2, 'price' => 10900],
                ['place_id' => 32, 'vehicle_id' => 2, 'price' => 8500],
                ['place_id' => 33, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 34, 'vehicle_id' => 2, 'price' => 4000],
                ['place_id' => 35, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 36, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 37, 'vehicle_id' => 2, 'price' => 8000],
                ['place_id' => 38, 'vehicle_id' => 2, 'price' => 7500],
                ['place_id' => 39, 'vehicle_id' => 2, 'price' => 4900],
                ['place_id' => 40, 'vehicle_id' => 2, 'price' => 3900],
                ['place_id' => 41, 'vehicle_id' => 2, 'price' => 10900],
                ['place_id' => 42, 'vehicle_id' => 2, 'price' => 8500],
                ['place_id' => 43, 'vehicle_id' => 2, 'price' => 9000],
                ['place_id' => 44, 'vehicle_id' => 2, 'price' => 5400],
                ['place_id' => 45, 'vehicle_id' => 2, 'price' => 7900],
                ['place_id' => 46, 'vehicle_id' => 2, 'price' => 5900],
                ['place_id' => 47, 'vehicle_id' => 2, 'price' => 6400],
                ['place_id' => 48, 'vehicle_id' => 2, 'price' => 6900],
                ['place_id' => 49, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 50, 'vehicle_id' => 2, 'price' => 6900],
                ['place_id' => 51, 'vehicle_id' => 2, 'price' => 4000],
                ['place_id' => 52, 'vehicle_id' => 2, 'price' => 10900],
                ['place_id' => 53, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 54, 'vehicle_id' => 2, 'price' => 8400],
                ['place_id' => 55, 'vehicle_id' => 2, 'price' => 7900],
                ['place_id' => 56, 'vehicle_id' => 2, 'price' => 9000],
                ['place_id' => 57, 'vehicle_id' => 2, 'price' => 10900],
                ['place_id' => 58, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 59, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 60, 'vehicle_id' => 2, 'price' => 5400],
                ['place_id' => 61, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 62, 'vehicle_id' => 2, 'price' => 6400],
                ['place_id' => 63, 'vehicle_id' => 2, 'price' => 3400],
                ['place_id' => 64, 'vehicle_id' => 2, 'price' => 8900],
                ['place_id' => 65, 'vehicle_id' => 2, 'price' => 10400],
                ['place_id' => 66, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 67, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 68, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 69, 'vehicle_id' => 2, 'price' => 9500],
                ['place_id' => 70, 'vehicle_id' => 2, 'price' => 9500],
                ['place_id' => 71, 'vehicle_id' => 2, 'price' => 13000],
                ['place_id' => 72, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 73, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 74, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 75, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 76, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 77, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 78, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 79, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 80, 'vehicle_id' => 2, 'price' => 9500],
                ['place_id' => 81, 'vehicle_id' => 2, 'price' => 7500],
                ['place_id' => 82, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 83, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 84, 'vehicle_id' => 2, 'price' => 6900],
                ['place_id' => 85, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 86, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 87, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 88, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 89, 'vehicle_id' => 2, 'price' => 7500],
                ['place_id' => 90, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 91, 'vehicle_id' => 2, 'price' => 11500],
                ['place_id' => 92, 'vehicle_id' => 2, 'price' => 3400],
                ['place_id' => 93, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 94, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 95, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 96, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 97, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 98, 'vehicle_id' => 2, 'price' => 4000],
                ['place_id' => 99, 'vehicle_id' => 2, 'price' => 3400],
                ['place_id' => 100, 'vehicle_id' => 2, 'price' => 3500],
                ['place_id' => 101, 'vehicle_id' => 2, 'price' => 8400],
                ['place_id' => 102, 'vehicle_id' => 2, 'price' => 5400],
                ['place_id' => 103, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 104, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 105, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 106, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 107, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 108, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 109, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 110, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 111, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 112, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 113, 'vehicle_id' => 2, 'price' => 4900],
                ['place_id' => 114, 'vehicle_id' => 2, 'price' => 5400],
                ['place_id' => 115, 'vehicle_id' => 2, 'price' => 3900],
                ['place_id' => 116, 'vehicle_id' => 2, 'price' => 3500],
                ['place_id' => 117, 'vehicle_id' => 2, 'price' => 7500],
                ['place_id' => 118, 'vehicle_id' => 2, 'price' => 4500],
                ['place_id' => 119, 'vehicle_id' => 2, 'price' => 5000],
                ['place_id' => 120, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 121, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 122, 'vehicle_id' => 2, 'price' => 7000],
                ['place_id' => 123, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 124, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 125, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 126, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 127, 'vehicle_id' => 2, 'price' => 6000],
                ['place_id' => 128, 'vehicle_id' => 2, 'price' => 4000],
                ['place_id' => 129, 'vehicle_id' => 2, 'price' => 5400],
                ['place_id' => 130, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 131, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 132, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 133, 'vehicle_id' => 2, 'price' => 6500],
                ['place_id' => 134, 'vehicle_id' => 2, 'price' => 5400],

                ['place_id' => 135, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 136, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 137, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 138, 'vehicle_id' => 2, 'price' => 5500],
                ['place_id' => 139, 'vehicle_id' => 2, 'price' => 5500],
            ]
        );

        // busprijzen
        DB::table('place_vehicle')->insert(
            [
                ['place_id' => 1, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 2, 'vehicle_id' => 3, 'price' => 11500],
                ['place_id' => 3, 'vehicle_id' => 3, 'price' => 9000],
                ['place_id' => 4, 'vehicle_id' => 3, 'price' => 10400],
                ['place_id' => 5, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 6, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 7, 'vehicle_id' => 3, 'price' => 5000],
                ['place_id' => 8, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 9, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 10, 'vehicle_id' => 3, 'price' => 16400],
                ['place_id' => 11, 'vehicle_id' => 3, 'price' => 6900],
                ['place_id' => 12, 'vehicle_id' => 3, 'price' => 9000],
                ['place_id' => 13, 'vehicle_id' => 3, 'price' => 13500],
                ['place_id' => 14, 'vehicle_id' => 3, 'price' => 11500],
                ['place_id' => 15, 'vehicle_id' => 3, 'price' => 9000],
                ['place_id' => 16, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 17, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 18, 'vehicle_id' => 3, 'price' => 9500],
                ['place_id' => 19, 'vehicle_id' => 3, 'price' => 9500],
                ['place_id' => 20, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 21, 'vehicle_id' => 3, 'price' => 8500],
                ['place_id' => 22, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 23, 'vehicle_id' => 3, 'price' => 8400],
                ['place_id' => 24, 'vehicle_id' => 3, 'price' => 12400],
                ['place_id' => 25, 'vehicle_id' => 3, 'price' => 8500],
                ['place_id' => 26, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 27, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 28, 'vehicle_id' => 3, 'price' => 9500],
                ['place_id' => 29, 'vehicle_id' => 3, 'price' => 16000],
                ['place_id' => 30, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 31, 'vehicle_id' => 3, 'price' => 12500],
                ['place_id' => 32, 'vehicle_id' => 3, 'price' => 10000],
                ['place_id' => 33, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 34, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 35, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 36, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 37, 'vehicle_id' => 3, 'price' => 9000],
                ['place_id' => 38, 'vehicle_id' => 3, 'price' => 8500],
                ['place_id' => 39, 'vehicle_id' => 3, 'price' => 6400],
                ['place_id' => 40, 'vehicle_id' => 3, 'price' => 5000],
                ['place_id' => 41, 'vehicle_id' => 3, 'price' => 12400],
                ['place_id' => 42, 'vehicle_id' => 3, 'price' => 11000],
                ['place_id' => 43, 'vehicle_id' => 3, 'price' => 10400],
                ['place_id' => 44, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 45, 'vehicle_id' => 3, 'price' => 9500],
                ['place_id' => 46, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 47, 'vehicle_id' => 3, 'price' => 7900],
                ['place_id' => 48, 'vehicle_id' => 3, 'price' => 7900],
                ['place_id' => 49, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 50, 'vehicle_id' => 3, 'price' => 7900],
                ['place_id' => 51, 'vehicle_id' => 3, 'price' => 5000],
                ['place_id' => 52, 'vehicle_id' => 3, 'price' => 12400],
                ['place_id' => 53, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 54, 'vehicle_id' => 3, 'price' => 9400],
                ['place_id' => 55, 'vehicle_id' => 3, 'price' => 8900],
                ['place_id' => 56, 'vehicle_id' => 3, 'price' => 10400],
                ['place_id' => 57, 'vehicle_id' => 3, 'price' => 12400],
                ['place_id' => 58, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 59, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 60, 'vehicle_id' => 3, 'price' => 6400],
                ['place_id' => 61, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 62, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 63, 'vehicle_id' => 3, 'price' => 4900],
                ['place_id' => 64, 'vehicle_id' => 3, 'price' => 9900],
                ['place_id' => 65, 'vehicle_id' => 3, 'price' => 11900],
                ['place_id' => 66, 'vehicle_id' => 3, 'price' => 6400],
                ['place_id' => 67, 'vehicle_id' => 3, 'price' => 6400],
                ['place_id' => 68, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 69, 'vehicle_id' => 3, 'price' => 11500],
                ['place_id' => 70, 'vehicle_id' => 3, 'price' => 10900],
                ['place_id' => 71, 'vehicle_id' => 3, 'price' => 14400],
                ['place_id' => 72, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 73, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 74, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 75, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 76, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 77, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 78, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 79, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 80, 'vehicle_id' => 3, 'price' => 11500],
                ['place_id' => 81, 'vehicle_id' => 3, 'price' => 9000],
                ['place_id' => 82, 'vehicle_id' => 3, 'price' => 8400],
                ['place_id' => 83, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 84, 'vehicle_id' => 3, 'price' => 7900],
                ['place_id' => 85, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 86, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 87, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 88, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 89, 'vehicle_id' => 3, 'price' => 9000],
                ['place_id' => 90, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 91, 'vehicle_id' => 3, 'price' => 13000],
                ['place_id' => 92, 'vehicle_id' => 3, 'price' => 4400],
                ['place_id' => 93, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 94, 'vehicle_id' => 3, 'price' => 8400],
                ['place_id' => 95, 'vehicle_id' => 3, 'price' => 8400],
                ['place_id' => 96, 'vehicle_id' => 3, 'price' => 7900],
                ['place_id' => 97, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 98, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 99, 'vehicle_id' => 3, 'price' => 4500],
                ['place_id' => 100, 'vehicle_id' => 3, 'price' => 5000],
                ['place_id' => 101, 'vehicle_id' => 3, 'price' => 9400],
                ['place_id' => 102, 'vehicle_id' => 3, 'price' => 6900],
                ['place_id' => 103, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 104, 'vehicle_id' => 3, 'price' => 8400],
                ['place_id' => 105, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 106, 'vehicle_id' => 3, 'price' => 8400],
                ['place_id' => 107, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 108, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 109, 'vehicle_id' => 3, 'price' => 6500],
                ['place_id' => 110, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 111, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 112, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 113, 'vehicle_id' => 3, 'price' => 5900],
                ['place_id' => 114, 'vehicle_id' => 3, 'price' => 6400],
                ['place_id' => 115, 'vehicle_id' => 3, 'price' => 4500],
                ['place_id' => 116, 'vehicle_id' => 3, 'price' => 4500],
                ['place_id' => 117, 'vehicle_id' => 3, 'price' => 8500],
                ['place_id' => 118, 'vehicle_id' => 3, 'price' => 6000],
                ['place_id' => 119, 'vehicle_id' => 3, 'price' => 6400],
                ['place_id' => 120, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 121, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 122, 'vehicle_id' => 3, 'price' => 8400],
                ['place_id' => 123, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 124, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 125, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 126, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 127, 'vehicle_id' => 3, 'price' => 7500],
                ['place_id' => 128, 'vehicle_id' => 3, 'price' => 5500],
                ['place_id' => 129, 'vehicle_id' => 3, 'price' => 6900],
                ['place_id' => 130, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 131, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 132, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 133, 'vehicle_id' => 3, 'price' => 8000],
                ['place_id' => 134, 'vehicle_id' => 3, 'price' => 6900],
                ['place_id' => 135, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 136, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 137, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 138, 'vehicle_id' => 3, 'price' => 7000],
                ['place_id' => 139, 'vehicle_id' => 3, 'price' => 7000],
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('place_vehicle');
    }
};
