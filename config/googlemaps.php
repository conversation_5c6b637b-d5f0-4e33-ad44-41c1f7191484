<?php

return [

    /*
    |--------------------------------------------------------------------------
    | API Key
    |--------------------------------------------------------------------------
    |
    | Will be used for all web services,
    | unless overwritten bellow using 'key' parameter
    |
    |
    */

    'key' => 'AIzaSyBKWQBCUyUDlyyldpdWxCkUoXlHV2P-7Cs',

    /*
    |--------------------------------------------------------------------------
    | Verify SSL Peer
    |--------------------------------------------------------------------------
    |
    | Will be used for all web services to verify
    | SSL peer (SSL certificate validation)
    |
     */
    'ssl_verify_peer' => false,

    /*
     |--------------------------------------------------------------------------
     | CURL's connection timeout
     |--------------------------------------------------------------------------
     |
     | Will be used for all web services to limit
     | the maximum time tha connection can take in seconds
     |
      */
    'connection_timeout' => 5,

    /*
     |--------------------------------------------------------------------------
     | CURL's request timeout
     |--------------------------------------------------------------------------
     |
     | Will be used for all web services to limit
     | the maximum time a request can take
     |
      */
    'request_timeout' => 30,

    /*
     |--------------------------------------------------------------------------
     | CURL's CURLOPT_ENCODING
     |--------------------------------------------------------------------------
     |
     | Will be used for all web services to use compression on requests.
     |
     | Sets the contents of the "Accept-Encoding:" header a containing all
     | supported encoding types.
     |
      */
    'request_use_compression' => false,

    /*
    |--------------------------------------------------------------------------
    | Service URL
    |--------------------------------------------------------------------------
    | url - web service URL
    | type - request type POST or GET
    | key - API key, if different to API key above
    | endpoint - boolean, indicates whenever output parameter to be used in the request or not
    | responseDefaultKey - specify default field value to be returned when calling getByKey()
    | param - accepted request parameters
    |
    */

    'service' => [

        'geocoding' => [
            'url' => 'https://maps.googleapis.com/maps/api/geocode/',
            'type' => 'GET',
            'key' => null,
            'endpoint' => true,
            'responseDefaultKey' => 'place_id',
            'param' => [
                'address' => null,
                'bounds' => null,
                'key' => null,
                'region' => null,
                'language' => null,
                'result_type' => null,
                'location_type' => null,
                'latlng' => null,
                'place_id' => null,
                'components' => [
                    'route' => null,
                    'locality' => null,
                    'administrative_area' => null,
                    'postal_code' => null,
                    'country' => null,
                ],
            ],
        ],

        // Deprecated
        'directions' => [
            'url' => 'https://maps.googleapis.com/maps/api/directions/',
            'type' => 'GET',
            'key' => null,
            'endpoint' => true,
            'responseDefaultKey' => 'geocoded_waypoints',
            'decodePolyline' => true, // true = decode overview_polyline.points to an array of points
            'param' => [
                'origin' => null, // required
                'destination' => null, // required
                'mode' => null,
                'waypoints' => null,
                'place_id' => null,
                'alternatives' => null,
                'avoid' => null,
                'language' => null,
                'units' => null,
                'region' => null,
                'departure_time' => null,
                'arrival_time' => null,
                'transit_mode' => null,
                'transit_routing_preference' => null,
            ],
        ],

        // Deprecated
        'distancematrix' => [
            'url' => 'https://maps.googleapis.com/maps/api/distancematrix/',
            'type' => 'GET',
            'key' => null,
            'endpoint' => true,
            'responseDefaultKey' => 'origin_addresses',
            'param' => [
                'origins' => null,
                'destinations' => null,
                'key' => null,
                'mode' => null,
                'language' => null,
                'avoid' => null,
                'units' => null,
                'departure_time' => null,
                'arrival_time' => null,
                'transit_mode' => null,
                'transit_routing_preference' => null,

            ],
        ],

        'placeautocomplete' => [
            'url' => 'https://maps.googleapis.com/maps/api/place/autocomplete/',
            'type' => 'GET',
            'key' => null,
            'endpoint' => true,
            'responseDefaultKey' => 'predictions',
            'param' => [
                'key' => null,
                'input' => null,
                'offset' => null,
                'location' => null,
                'radius' => null,
                'language' => null,
                'types' => null, // use string as parameter
                'type' => null, // types deprecated, one type may be specified
                'components' => null,
            ],
        ],

        'placequeryautocomplete' => [
            'url' => 'https://maps.googleapis.com/maps/api/place/queryautocomplete/',
            'type' => 'GET',
            'key' => null,
            'endpoint' => true,
            'responseDefaultKey' => 'predictions',
            'param' => [
                'key' => null,
                'input' => null,
                'offset' => null,
                'location' => null,
                'radius' => null,
                'language' => null,
            ],
        ],

        // NEW
        // https://developers.google.com/maps/documentation/routes/reference/rest
        'routes' => [
            'url'                   => 'https://routes.googleapis.com/directions/v2:computeRoutes',
            'type'                  => 'POST',
            'key'                   =>  null,
            'endpoint'              =>  true,
            'decodePolyline'        =>  true, // true = decode overview_polyline.points to an array of points
            'param'                         => [
                'origin'                    => null, // required
                'destination'               => null, //required
                'intermediates'             => null,
                'travelMode'                => null,
                'transitRoutingPreference'  => null,
                'polylineQuality'           => null,
                'polylineEncoding'          => null,
                'departureTime'             => null,
                'arrivalTime'               => null,
                'computeAlternativeRoutes'  => null,
                'routeModifiers'            => null,
                'languageCode'              => null,
                'regionCode'                => null,
                'units'                     => null,
                'optimizeWaypointOrder'     => false,
                'requestedReferenceRoutes'  => null,
                'extraComputations'         => null,
                'trafficModel'              => null,
                'transitPreferences'        => null,
            ]
        ],

        // NEW
        // https://developers.google.com/maps/documentation/routes/reference/rest/v2/TopLevel/computeRouteMatrix
        'routematrix' => [
            'url'                   => 'https://routes.googleapis.com/distanceMatrix/v2:computeRouteMatrix',
            'type'                  => 'POST',
            'key'                   =>  null,
            'endpoint'              =>  true,
            'param'                         => [
                'origins'                   => null, // required
                'destinations'              => null, //required
                'travelMode'                => null,
                'routingPreference'         => null,
                'departureTime'             => null,
                'arrivalTime'               => null,
                'languageCode'              => null,
                'regionCode'                => null,
                'units'                     => null,
                'extraComputations'         => null,
                'trafficModel'              => null,
                'transitPreferences'        => null,
            ]
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | End point
    |--------------------------------------------------------------------------
    |
    |
    */

    'endpoint' => [
        'xml' => 'xml?',
        'json' => 'json?',
    ],

];
