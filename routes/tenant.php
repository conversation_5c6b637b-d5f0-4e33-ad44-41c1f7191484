<?php

declare(strict_types=1);

use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\MolliePaymentController;
use App\Http\Controllers\ReservationController;
use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {

    Volt::route('test', 'test')->name('test');
    Route::get('/g', function () { return view('g'); })->name('g');

    // Home - Form
    Route::get('/', function () {

        $initialState = session()->get('state');

        $stepName = $initialState ? array_key_last($initialState) : false;

        return view('home', compact('stepName', 'initialState'));

    })->name('home');


    // Payment Routes
    Route::get('reservering/{reservation_rand_id}', [ReservationController::class, 'show'])->name('reservation');

    // Mollie webhook (public route, no CSRF protection)
    Route::post('/webhooks/mollie', [MolliePaymentController::class, 'handleWebhook'])
        ->name('webhooks.mollie')
        ->withoutMiddleware(['web', 'csrf']);

    // Cancel Reservation
    Route::get('annuleren/{rand_id}', function (string $rand_id) {

        \App\Models\Reservation::where('rand_id', $rand_id)->update([
            'status' => \App\Enums\ReservationStatus::CANCELLED
        ]);

        return view('cancelled');

    })->name('cancelled');


    Route::middleware('guest')->group(function () {

        Volt::route('register', 'pages.auth.register')
            ->name('register');

        Volt::route('login', 'pages.auth.login')
            ->name('login');

        Volt::route('forgot-password', 'pages.auth.forgot-password')
            ->name('password.request');

        Volt::route('reset-password/{token}', 'pages.auth.reset-password')
            ->name('password.reset');
    });


    Route::middleware('auth')->group(function () {

        Route::get('logout', function () {

            auth()->logout();

            return redirect('/');

        })->name('logout');

        Volt::route('profile', 'pages.profile')->name('profile');

        Volt::route('verify-email', 'pages.auth.verify-email')->name('verification.notice');

        Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)->middleware(['signed', 'throttle:6,1'])->name('verification.verify');

        Volt::route('confirm-password', 'pages.auth.confirm-password')->name('password.confirm');

        Route::middleware(['verified'])->group(function () {
            Volt::route('addresses', 'pages.addresses')->name('addresses');
            Volt::route('addressesnew', 'pages.addressesnew')->name('addressesnew');
            Volt::route('same', 'pages.same')->name('same');
            Volt::route('reservations', 'pages.reservations')->name('reservations');
            Volt::route('dashboard', 'pages.dashboard')->name('dashboard');
        });


        Route::get('/download-example-csv', function () {
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="example-places-import.csv"',
            ];

            $content = "Place/Zipcode,sedan,stationcar,van\nAlmere,1000,1000,1500";

            return response($content, 200, $headers);
        })->name('download.example.csv');

    });
});
