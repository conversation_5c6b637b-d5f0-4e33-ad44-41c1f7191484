<?php

use App\Http\Controllers\Auth\VerifyEmailController;
use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;
use Stancl\Tenancy\Database\Models\Domain;

foreach (config('tenancy.central_domains') as $domain) {
    Route::domain($domain)->group(function () {

        // Route::get('mapstest', function(){  });

        Route::get('/test', function () {

            $domains = Domain::all();
            foreach ($domains as $domain) {
                echo 'Tenant ID: '.$domain->tenant_id.'<br>';
                echo 'Domain: '.$domain->domain.'<br><br>';
            }

        });

        // Route::middleware('guest')->group(function () {
        //     Volt::route('register', 'pages.auth.register')
        //         ->name('register.central');

        //     Volt::route('login', 'pages.auth.login')->name('login.central');

        //     Volt::route('forgot-password', 'pages.auth.forgot-password')
        //         ->name('password.request.central');

        //     Volt::route('reset-password/{token}', 'pages.auth.reset-password')
        //         ->name('password.reset.central');
        // });

        // Route::middleware('auth')->group(function () {

        //     Route::view('dashboard', 'dashboard')
        //         ->middleware(['verified'])
        //         ->name('dashboard.central');

        //     Route::view('profile', 'profile')
        //         ->name('profile.central');

        //     Volt::route('verify-email', 'pages.auth.verify-email')
        //         ->name('verification.notice.central');

        //     Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
        //         ->middleware(['signed', 'throttle:6,1'])
        //         ->name('verification.verify.central');

        //     Volt::route('confirm-password', 'pages.auth.confirm-password')
        //         ->name('password.confirm.central');

        // });

    });
}
