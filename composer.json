{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "version": "1", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "alexpechkarev/google-maps": "^12.1", "filament/filament": "^3.3", "grkamil/laravel-telegram-logging": "^1.13", "laravel/framework": "^12.4", "laravel/tinker": "^2.10", "league/csv": "^9.23", "livewire/flux": "^2.2", "livewire/flux-pro": "^2.2", "livewire/livewire": "^3.6", "livewire/volt": "^1.7", "mollie/laravel-mollie": "^3.1", "saade/filament-laravel-log": "^3.0", "spatie/laravel-google-calendar": "^3.8.3", "spatie/laravel-livewire-wizard": "^2.4.2", "stancl/tenancy": "^3.9.1"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.24", "larastan/larastan": "^3.0", "laravel/pail": "^1.1", "laravel/pint": "^1.21", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.7", "pestphp/pest": "^3.7.4", "pestphp/pest-plugin-laravel": "^3.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Database\\Seeders\\Tenant\\": "database/seeders/tenant/"}, "files": ["app/Helpers/Helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan ide-helper:generate"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["vendor/bin/phpstan", "php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}}}