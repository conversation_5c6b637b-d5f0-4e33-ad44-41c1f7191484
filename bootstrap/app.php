<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->group('universal', [InitializeTenancyByDomain::class]);

        // TODO: enable trustedhosts and implement domains cache. Look at tenancyserviceprovider::domaincreated//
        // $middleware->trustHosts(at: fn () => config('app.trusted_hosts'));
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
