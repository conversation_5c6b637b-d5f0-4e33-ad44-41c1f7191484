<?php
use Livewire\Attributes\{Layout, Title};
use Livewire\Volt\Component;
use App\Models\Reservation;
// use Livewire\Attributes\On;
use Carbon\Carbon;

new class extends Component {

    public function with(): array
    {
        return [
            'reservations' => auth()->user()->reservations,
        ];
    }
    
}; ?>

<div class="border rounded-lg border-zinc-200 dark:border-zinc-700">
    <div class="flex items-center justify-between px-5 py-3 border-b rounded-t-lg bg-zinc-50 dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700">
        <h1 class="text-xl font-bold leading-5 text-zinc-900 dark:text-zinc-100"> {{ __('Reservations') }} </h1>
        <flux:button type="button" icon="plus" href="{{route('home')}}"> {{ __('Add reservation') }} </flux:button>
    </div>

    <div class="px-4 py-6 md:px-6">
        <flux:table>
            <flux:table.columns>
                <flux:table.column>Geplaats op</flux:table.column>
                <flux:table.column>Datum</flux:table.column>
                <flux:table.column>Rit</flux:table.column>
                <flux:table.column>Prijs</flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($reservations as $r)

                @php $has_waypoints = (count($r->data['waypoints']) > 2) ? true : false; @endphp

                <flux:table.row>
                    <flux:table.cell>{{ Carbon::parse($r->created_at)->diffForHumans(null, true, true) }} geleden</flux:table.cell>
                    <flux:table.cell>{{ Carbon::parse($r->datetime)->format('H:i d.m.Y') }}</flux:table.cell>
                    <flux:table.cell>
                        @foreach ($r->data['waypoints'] as $waypoint)
                            <b>
                            @if ($loop->first) Van:
                            @elseif (!$loop->first && !$loop->last && $has_waypoints) Tussenstop:
                            @elseif ($loop->last) Bestemming:
                            @endif
                            </b>
                            {{-- @php dump($waypoint); @endphp --}}
                            @if (!empty($waypoint['airport'])) {{ $waypoint['airport'] }}
                            @else
                                {{ $waypoint['route'] ?? '' }}
                                {{ $waypoint['street_number'] ?? '' }},
                                {{ $waypoint['locality'] }}
                            @endif
                            <br>
                        @endforeach
                    </flux:table.cell>
                    
                    <flux:table.cell variant="strong"> {{ App\Helpers\Helper::strfmon($r->data['price']) }} </flux:table.cell>
                </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>
    </div>
</div>