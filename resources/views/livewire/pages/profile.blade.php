<?php
use Livewire\Attributes\{Layout, Title};
use Livewire\Volt\Component;

new
#[Layout('components.layouts.app')]
#[Title('Addresses')]
class extends Component {
    //
}; ?>

<div class="border rounded-lg border-zinc-200 dark:border-zinc-700">

    <div class="flex items-center justify-between px-5 py-5 border-b rounded-t-lg bg-zinc-50 dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700">
        <h1 class="text-xl font-bold leading-5 text-zinc-900 dark:text-zinc-100"> {{ __('Profile') }} </h1>
    </div>

    <div class="px-4 py-6 md:px-6">
        <div class="mx-auto space-y-6 max-w-7xl sm:px-6 lg:px-8">
            <div class="p-4 bg-white shadow-sm sm:p-8 dark:bg-gray-800 sm:rounded-lg">
                <div class="max-w-xl">
                    <livewire:profile.update-profile-information-form />
                </div>
            </div>

            <div class="p-4 bg-white shadow-sm sm:p-8 dark:bg-gray-800 sm:rounded-lg">
                <div class="max-w-xl">
                    <livewire:profile.update-password-form />
                </div>
            </div>

            <div class="p-4 bg-white shadow-sm sm:p-8 dark:bg-gray-800 sm:rounded-lg">
                <div class="max-w-xl">
                    <livewire:profile.delete-user-form />
                </div>
            </div>
        </div>
    </div>
</div>
