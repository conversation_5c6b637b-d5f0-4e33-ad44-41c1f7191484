<?php
use Livewire\Attributes\{Layout, Title};
use Livewire\Volt\Component;
use App\Models\Address;
use App\Helpers\GoogleMapsHelper;

// use Livewire\Attributes\On;

new
#[Layout('components.layouts.app')]
#[Title('Addresses')]
class extends Component {

    public function with(): array
    {
        return [
            'addresses' => auth()->user()->addresses,
        ];
    }

    public function delete($id)
    {
        $address = auth()->user()->addresses()->findOrFail($id);
        $address->delete();
        Flux::toast(variant: 'success', text: '<PERSON><PERSON> is succesvol verwijderd!.');
    }


    /**
     * @param $address = array <str formatted_address, str place_id>
     *
     */
    public function save( $address )
    {

        if( $address = GoogleMapsHelper::getPlace( $address, true ) ){

            $address = Address::create([
                'address' => $address,
            ]);

            $this->dispatch('address-created');

            Flux::toast(variant: 'success', text: 'Adres is succesvol opgeslagen!');

            return;

        }

        Flux::toast(variant: 'danger', text: 'Something went wrong!');

        return;
    }

}; ?>
<zerre
x-data="googlePlacesAutocomplete()"
x-init="init()"
x-on:address-created="reset()"
>
    <div class="border rounded-lg border-zinc-200">

        <div class="flex items-center justify-between px-5 py-3 border-b rounded-t-lg bg-zinc-50 border-zinc-200">
            <h1 class="text-xl font-bold leading-5 text-zinc-900"> {{ __('Addresses') }} </h1>
            <flux:button type="button" icon="plus" x-on:click="formOpen = !formOpen" href="#"> {{ __('Add address') }} </flux:button>
        </div>

        <div class="px-4 py-6">
            <form x-show="formOpen" x-transition x-on:submit.prevent="" class="pb-8 mb-4 border-b">
                <flux:heading size="lg">Nieuw adres toevoegen</flux:heading>
                <flux:subheading>Nog makkelijker boeken met opgeslagen adressen.</flux:subheading>

                <flux:input.group class="relative mt-4" x-on:click.outside="closeDropdown()">
                    <flux:input
                        x-model="query"
                        x-on:input="searchPlaces()"
                        x-on:keydown.down.prevent="highlightNext()"
                        x-on:keydown.up.prevent="highlightPrevious()"

                        x-on:keydown.enter.prevent="handleEnter()"

                        x-on:focus="isOpen = true"
                        x-on:keydown.escape.window="closeDropdown()"
                        placeholder="Vul hier uw adres in"
                        autocomplete="off"
                        x-ref="inputElement"
                        class="relative"
                    />

                    <ul x-show="isOpen && suggestions.length > 0" x-transition
                        class="absolute z-10 w-full mt-12 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-2 ring-black/5 focus:outline-hidden sm:text-sm drop-shadow-xl"
                        role="listbox" x-ref="listbox">
                        <template x-for="(suggestion, index) in suggestions" :key="suggestion.id + '-' + index">
                            <li
                                {{-- x-on:click="selectHighlighted()" --}}
                                x-on:click="selectSuggestion(suggestion)"
                                x-on:mouseenter="highlightedIndex = index"
                                :class="{ 'text-white bg-indigo-600': highlightedIndex === index, 'text-gray-900': highlightedIndex !== index }"
                                class="relative py-3 pl-3 cursor-pointer select-none group pr-9"
                                role="option"
                            >

                                {{-- selected checkmark --}}
                                <span class="block truncate" :class="{ 'font-semibold': (selectedPlace &&selectedPlace.id == suggestion.id) }">
                                    <span x-text="suggestion.text"></span>
                                    <span class="text-gray-400 group-hover:text-indigo-200" x-text="suggestion.text.country"></span>
                                </span>
                                <span x-show="(selectedPlace && selectedPlace.id == suggestion.id)" class="absolute inset-y-0 right-0 flex items-center pr-4" :class="{ 'text-white': highlightedIndex === index, 'text-indigo-600': highlightedIndex !== index }">
                                <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" /></svg>
                                </span>

                            </li>
                        </template>
                    </ul>


                    <flux:button icon="check" x-ref="submitButton" x-on:click="$wire.save( $refs.inputElement.value )" href="#">Opslaan</flux:button>
                </flux:input.group>

            </form>

            <flux:table>
                <flux:table.columns>
                    <flux:table.column>Adres</flux:table.column>
                    <flux:table.column></flux:table.column>
                </flux:table.columns>

                <flux:table.rows>
                    @foreach ($addresses as $item)
                    <flux:table.row>
                        <flux:table.cell>{{ $item->address['formatted_address'] }}</flux:table.cell>
                        <flux:table.cell class="text-right"><flux:button icon="trash" size="sm" wire:click="delete({{$item->id}})" wire:confirm="Zeker weten?" href="#" >Delete</flux:button></flux:table.cell>
                    </flux:table.row>
                    @endforeach
                </flux:table.rows>
            </flux:table>


        </div>
    </div>

<!-- 1. Google Maps Bootstrap Loader is placed first in the head -->
<script>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
    ({key: "{{ @tenant('googlemaps')['key'] }}", v: "weekly"});</script>
<style>
    .transition-enter-active, .transition-leave-active { transition: opacity 0.1s ease, transform 0.1s ease; }
    .transition-enter-from, .transition-leave-to { opacity: 0; transform: scale(0.95); }
    .transition-enter-to, .transition-leave-from { opacity: 1; transform: scale(1); }
</style>
<flux:toast />

<script>
function googlePlacesAutocomplete() {
    return {
        query: '',
        suggestions: [],
        selectedPlace: null,
        isOpen: false,
        highlightedIndex: -1,
        places: null,
        isApiReady: false,
        formOpen: false,

        async init() {
            try {
                // This now runs safely because the bootstrap loader has already created the `google` object.
                const { Place, AutocompleteSuggestion } = await google.maps.importLibrary("places");
                this.places = { Place, AutocompleteSuggestion };
                this.isApiReady = true;
            } catch (error) {
                console.error("Failed to load Google Places library", error);
            }
        },

        async searchPlaces() {
            if (this.query.length < 2 || !this.isApiReady) {
                this.suggestions = [];
                return;
            }
            const request = {
                input: this.query,
                locationRestriction: {
                west: -5.15,
                north: 55.10,
                east: 15.50,
                south: 42.30,
                },
                origin: { lat: 52.3105, lng: 4.7683 }, // Schiphol Airport
                language: "nl-NL",
                region: "eu",
            };

            try {
                const { suggestions:rawSuggestions } = await this.places.AutocompleteSuggestion.fetchAutocompleteSuggestions(request);

                // 3. THE KEY STEP: Transform the raw data into a  simple array
                if (rawSuggestions) {
                    this.suggestions = rawSuggestions.map(suggestion => {
                        return {

                            text: suggestion.placePrediction.text.text,
                            id: suggestion.placePrediction.placeId
                        };
                    });
                }

            } catch (error) {
                this.suggestions = [];
            }
        },

        async selectSuggestion(suggestion) {

            if (!this.isApiReady) return;

            this.query = suggestion.text;
            this.selectedPlace = suggestion;

            this.closeDropdown();
        },

        closeDropdown() { this.isOpen = false; this.highlightedIndex = -1; },

        toggleDropdown() { this.isOpen = !this.isOpen; },

        reset() { this.query = ''; this.selectedPlace = null; this.suggestions = []; this.closeDropdown(); this.formOpen = false; },
        highlightNext() { if (this.suggestions.length > 0) this.highlightedIndex = (this.highlightedIndex + 1) % this.suggestions.length; },
        highlightPrevious() { if (this.suggestions.length > 0) this.highlightedIndex = (this.highlightedIndex - 1 + this.suggestions.length) % this.suggestions.length; },
        selectHighlighted() {
            if (this.highlightedIndex > -1){
                this.selectSuggestion(this.suggestions[this.highlightedIndex]);
            }
        },

        handleEnter(){
            if(this.selectedPlace && this.isOpen === false){
                this.$wire.save( this.$refs.inputElement.value )
            }
            else if(this.isOpen === true){
                this.selectHighlighted();
            };
        }
    }
}
</script>


</zerre>
