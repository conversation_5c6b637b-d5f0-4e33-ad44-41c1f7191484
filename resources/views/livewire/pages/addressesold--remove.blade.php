<?php
use Livewire\Attributes\{Layout, Title};
use Livewire\Volt\Component;
use App\Models\Address;
// use Livewire\Attributes\On;

new
#[Layout('components.layouts.app')]
#[Title('Addresses')]
class extends Component {

    public function with(): array
    {
        return [
            'addresses' => auth()->user()->addresses,
        ];
    }

    public function delete($id)
    {
        $address = auth()->user()->addresses()->findOrFail($id);
        $address->delete();
        Flux::toast(variant: 'success', text: '<PERSON>res is succesvol verwijderd!.');

    }


    /**
     * @param $address = array <str formatted_address, str place_id>
     *
     */
    public function save( $address )
    {
        // TODO: Request the address again from google api, that verifies, and cleans the data thatll be saved.
        // It wont be user input anymore.
        // Also we can then remove the place_id hidden input field, and everything assoc with that.
        // Instead We'll use the api response data.
        // Request from google geocoding for verification
        // $response = GoogleMapsFacade::load('geocoding')
        //     ->setParam(['address'=>$address])
        //     ->get();
        // $response = json_decode($response);

        // if($response->status=="OK"){

        //     return Helper::getComponentsFromResponse($response);

        // }
        $address = Address::create([
            'address' => $address,
        ]);

        $this->dispatch('address-created');
        Flux::toast(variant: 'success', text: 'Adres is succesvol opgeslagen!.');

    }

}; ?>
<div class="border rounded-lg border-zinc-200 dark:border-zinc-700" x-data="{ formOpen: false }" x-on:address-created="formOpen = false;document.querySelector('.autocomplete-input').value = '';">
    <div class="flex items-center justify-between px-5 py-3 border-b rounded-t-lg bg-zinc-50 dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700">
        <h1 class="text-xl font-bold leading-5 text-zinc-900 dark:text-zinc-100"> {{ __('Addresses') }} </h1>
        <flux:button type="button" icon="plus" x-on:click="formOpen = !formOpen" href="#"> {{ __('Add address') }} </flux:button>
    </div>

    <div class="px-4 py-6 md:px-6">
        <form x-show="formOpen" x-transition wire:submit.prevent="" class="p-4">
            <flux:heading size="lg">Nieuw adres toevoegen</flux:heading>
            <flux:subheading>Nog makkelijker boeken met opgeslagen adressen.</flux:subheading>

            <flux:input.group class="mt-4">
                <flux:input placeholder="{{__('Search Address')}}" class:input="autocomplete-input" />
                <flux:button icon="check" id="btnn" x-on:click="$wire.save( { address:$wire.$el.querySelector('.autocomplete-input').value, place_id:$wire.$el.querySelector('#place_id').value } )" href="#">Opslaan</flux:button>
            </flux:input.group>
            <input type="hidden" id="place_id">
        </form>

        <flux:table>
            <flux:table.columns>
                <flux:table.column>Adres</flux:table.column>
                <flux:table.column></flux:table.column>
            </flux:table.columns>

            <flux:table.rows>
                @foreach ($addresses as $item)
                <flux:table.row>
                    <flux:table.cell>{{ $item->address['address'] }}</flux:table.cell>
                    <flux:table.cell class="text-right"><flux:button icon="trash" size="sm" wire:click="delete({{$item->id}})" wire:confirm="Zeker weten?" href="#" >Delete</flux:button></flux:table.cell>
                </flux:table.row>
                @endforeach
            </flux:table.rows>
        </flux:table>


    </div>




<flux:toast />
@script
<script>

    let autocomplete = [];
    var options = { language: 'nl', types: ['establishment', 'geocode'], componentRestrictions: {country: ['nl', 'be']} };

    window.initMapsAutocomplete = function() { loopAllWaypointElements(); }

    function loopAllWaypointElements(){

        document.querySelectorAll('.autocomplete-input').forEach(function(element, index, arr) {

            // init GoogleMaps Autocomplete on all location input fields (waypoints).
            initAutocompleteForElement(element, index);

        });

    }

    function initAutocompleteForElement(element, index){

        autocomplete[index] = new google.maps.places.Autocomplete(element, options);

        google.maps.event.addListener(autocomplete[index], 'place_changed', function() {

            var place = this.getPlace();

            if(place.formatted_address){
                var address = place.formatted_address;
            }else{
                var address = place.name;
            }

            document.querySelector('#place_id').value = place.place_id;

        });

        element.addEventListener('keydown', function(e){

            if (e.keyCode == 13 && $('.pac-container:visible').length) {

                e.preventDefault();

                var place = this.getPlace();

                if(place.formatted_address){
                    var address = place.formatted_address;
                }else{
                    var address = place.name;
                }

                document.querySelector('#place_id').value = place.place_id;

            }

        });

    }

    $wire.hook('commit', ({ component, commit, respond, succeed, fail }) => {
        // Equivalent of 'message.sent'

        succeed(({ snapshot, effect }) => { // Equivalent of 'message.received'

            queueMicrotask(() => { // Equivalent of 'message.processed'

                setTimeout(function() { loopAllWaypointElements(); }, 3);

            })
        })

    })

    loadMaps();

</script>
@endscript

{{-- This is minified from front.blade.php, if it changes there the changes could be copied over to here. --}}
@section('styles')<style>.pac-container{background-color:#fff;position:absolute!important;z-index:1000;box-shadow:none;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;padding:0!important;border-radius:10px;border:1px solid #e4e4e7;margin-top:4px}.pac-item,.pac-item:first-child{border:none}.pac-container:after{background-image:none!important;height:0;margin:0;padding:0}.pac-item{display:block;cursor:default;padding:12px!important;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;line-height:30px;text-align:left;font-size:11px;color:#999;border-top:1px solid #ccc}.pac-item-query,.pac-item-query+span{margin:0!important;padding:0!important;color:#5c5b5b}.pac-item-selected,.pac-item-selected:hover,.pac-item:hover{background-color:#4f46e5}.pac-item-selected .pac-item-query,.pac-item-selected .pac-item-query+span,.pac-item-selected:hover .pac-item-query,.pac-item-selected:hover .pac-item-query+span,.pac-item:hover .pac-item-query,.pac-item:hover .pac-item-query+span{color:#fff}.pac-item-query{display:block;font-weight:800;font-size:15px;height:22px}.pac-item-query+span{font-size:14px}.pac-icon{display:none}.pac-icon-search{background-position:-1px -1px}.pac-item-selected .pac-icon-search{background-position:-18px -1px}.pac-icon-marker{background-position:-1px -161px}.pac-item-selected .pac-icon-marker{background-position:-18px -161px}.pac-placeholder{color:zinc}.filled input,.filled span{background:red!important}input[type=date],input[type=datetime-local],input[type=time]{position:relative}input[type=date]::-webkit-calendar-picker-indicator,input[type=datetime-local]::-webkit-calendar-picker-indicator,input[type=time]::-webkit-calendar-picker-indicator{background:0 0;cursor:pointer;height:32px;width:100%;position:absolute;left:0}</style>@endsection
</div>
