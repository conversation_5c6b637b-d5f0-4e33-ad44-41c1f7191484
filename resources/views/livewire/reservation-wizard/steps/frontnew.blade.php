<zerre>

@if (env('APP_ENV') != 'production') @include('livewire.reservation-wizard.navigation') @endif

<div class="border rounded-lg border-zinc-200 dark:border-zinc-700">
    <div class="px-4 py-5 border-b rounded-t-lg bg-zinc-50 dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700 sm:px-6">
        <h1 class="text-xl font-bold leading-5 text-[var(--color-accent-foreground)]-900 dark:text-zinc-100"><PERSON><PERSON> eenvoudig je taxirit</h1>
    </div>

    <form wire:submit="submit(Object.fromEntries(new FormData($event.target)))">
        <div class="px-4 py-6 md:px-6">
            <div>
                @if ($errors->any())
                <div class="p-4 mb-5 rounded-md bg-red-50">
                    <div class="flex">
                        <div class="shrink-0"><svg class="w-5 h-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" /></svg></div>
                        <div class="ml-3"><h3 class="text-sm font-medium text-red-800">Oeps! Er bevinden zich fouten in het formulier.</h3></div>
                    </div>
                </div>
                @endif
            </div>


            <div>
                <ul role="list" class="block">
                    @foreach ($waypoints as $key => $waypoint)

                        {{-- If waypoints_enabled, include add-waypoint-button just before the last loop --}}
                        @includeWhen(tenant('settings')['waypoints_enabled'], 'livewire.reservation-wizard.components.add-waypoint-button')

                        <li wire:key="waypoint-item-{{$key}}">
                            <div class="relative {{$loop->last ? 'pb-2' : ($addresses ? 'pb-4' : 'pb-8' )}}">
                                @if (!$loop->last) <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-zinc-200 hidden sm:block" aria-hidden="true"></span> @endif
                                <div class="relative flex space-x-3">
                                    <span class="flex items-center justify-center hidden w-8 h-8 rounded-full bg-zinc-400 ring-8 ring-white sm:block">{!!$waypoint['icon']!!}</span>

                                    <div class="w-full">
                                        <div class="flex rounded-md">

                                            <flux:input.group class="static">

                                                <flux:input.group.prefix class="w-16">{{ $waypoint['label'] }}</flux:input.group.prefix>

                                                <flux:input type="text" name="{{$waypoint['name']}}" data-key="{{$key}}" wire:model="waypoints.{{$key}}.value" class:input="autocomplete-input">

                                                    @if($waypoint['is_waypoint'])
                                                        <x-slot name="iconTrailing"> <flux:button size="sm" variant="subtle" icon="x-mark" class="-mr-1" wire:click="removeWaypoint('{{$key}}')" /> </x-slot>
                                                    @endif

                                                </flux:input>

                                            </flux:input.group>

                                        </div>

                                        @error($waypoint['name'])
                                            <div class="px-4 py-3 my-2 text-sm text-red-700 rounded-md bg-red-50"><span class="error">{{ $message }}</span></div>
                                        @enderror

                                    </div>

                                </div>
                            </div>
                        </li>
                    @endforeach

                </ul>

                <div class="max-w-2xl mt-6">

                    <flux:input type="datetime-local" wire:model="datetime" min="{{ now()->format('Y-m-d') }}" label="Wanneer wilt u worden opgehaald?" icon="calendar" />

                    @if($origin_is_airport)

                        <flux:input type="text" placeholder="Vluchtnummer" wire:model="flightnr" maxlength="10" class="mt-4" />

                    @endif


                    <div class="mt-6 mb-2"> <flux:checkbox wire:model.live="retour" label="Retour" /> </div>

                    @if($retour)

                        <flux:input type="datetime-local" wire:model="retour_datetime" min="{{ now()->format('Y-m-d\TH:m') }}" label="Retour datum & tijd" icon="calendar" />

                        @if($destination_is_airport)
                            <flux:input type="text" placeholder="Vluchtnummer" wire:model="retour_flightnr" maxlength="10" class="mt-4" />
                        @endif

                    @endif

                </div>

            </div>

        </div>

        @include('livewire.reservation-wizard.footer', ['button_text' => 'Bereken mijn ritprijs'])
    </form>
</div>


<script src="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-js"></script>
@script

@endscript
</zerre>
