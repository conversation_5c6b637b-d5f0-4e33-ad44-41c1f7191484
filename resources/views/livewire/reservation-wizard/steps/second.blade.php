<zerre> @include('livewire.reservation-wizard.navigation')
<form wire:submit="submit" class="overflow-hidden border rounded-lg border-zinc-200 dark:border-zinc-700">

    <div class="flex flex-col gap-8 p-6">
        @if ($errors->any())
        <div class="p-4 rounded-md bg-red-50">
            <div class="flex">
                <div class="shrink-0"><svg class="w-5 h-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" /></svg></div>
                <div class="ml-3"><h3 class="text-sm font-medium text-red-800">Oeps! Er bevinden zich fouten in het formulier.</h3></div>
            </div>
            <ul>
                @foreach ($errors->all() as $error)
                    <li class="text-sm font-medium text-red-800">{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif
        <div>
            <h2 class="mb-2 leading-5 font-medium text-[var(--color-accent)]">Aantal personen</h2>
            <select wire:model="people"
            class="block w-full py-2 pl-3 pr-10 mt-1 text-base border-gray-300 rounded-md focus:border-zinc-500 focus:outline-hidden focus:ring-zinc-500 sm:text-sm">
                @foreach ($people_count as $i)
                    <option value="{{$i}}">{{$i}}</option>
                @endforeach
            </select>
        </div>

        <div class="grid grid-cols-2 gap-4">
            <div>
                <h2 class="mb-2 leading-5 font-medium text-[var(--color-accent)]">Aantal koffers</h2>
                <select wire:model="luggage"
                class="block w-full py-2 pl-3 pr-10 mt-1 text-base border-gray-300 rounded-md focus:border-zinc-500 focus:outline-hidden focus:ring-zinc-500 sm:text-sm">
                    @foreach ($luggage_count as $i)
                        <option value="{{$i}}">{{$i}}</option>
                    @endforeach
                </select>
                @error('luggage')
                    <div class="px-4 py-3 my-2 text-sm text-red-800 rounded-md bg-red-50"><span class="error">{!! $message !!}</span></div>
                @enderror
            </div>
            <div>
                <h2 class="mb-2 leading-5 font-medium text-[var(--color-accent)]">Handbagage</h2>
                <select wire:model="handluggage"
                class="block w-full py-2 pl-3 pr-10 mt-1 text-base border-gray-300 rounded-md focus:border-zinc-500 focus:outline-hidden focus:ring-zinc-500 sm:text-sm">
                    @foreach ($handluggage_count as $i)
                        <option value="{{$i}}">{{$i}}</option>
                    @endforeach
                </select>
                @error('handluggage')
                    <div class="px-4 py-3 my-2 text-sm text-red-800 rounded-md bg-red-50"><span class="error">{!! $message !!}</span></div>
                @enderror
            </div>
        </div>
    </div>

    @include('livewire.reservation-wizard.footer', ['button_text' => 'Bereken mijn ritprijs'])

</form>
</zerre>
