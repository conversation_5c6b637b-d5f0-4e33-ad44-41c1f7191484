<zerre> @include('livewire.reservation-wizard.navigation')
    <form wire:submit="submit" class="overflow-hidden border rounded-lg border-zinc-200">

        <div class="px-4 py-5 space-y-4 bg-white">
            @foreach ($vehicles as $vehicle)
            <div>
                <input type="radio" wire:model.live="chosen_vehicle_id" value="{{$vehicle->id}}" class="sr-only peer" id="vehicle_{{$vehicle->id}}" aria-labelledby="{{$vehicle->name}}" aria-describedby="{{$vehicle->name}}">
                <label for="vehicle_{{$vehicle->id}}"
                class="relative grid grid-cols-2 px-6 py-4 border rounded-lg shadow-sm cursor-pointer focus:outline-hidden hover:shadow-lg hover:bg-zinc-100 peer-checked:ring-[var(--color-accent)] peer-checked:ring-2 ">

                <div class="flex flex-col md:flex-row-reverse">

                        <div class="flex flex-col w-full text-sm">
                            <div class="mb-4 text-lg font-bold text-zinc-700 md:mb-0">{{$vehicle->name}}</div>

                            <div class="flex-col hidden sm:flex">
                                @for ($i=1; $i<5; $i++)
                                    <span class="text-gray-500">
                                        <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="block w-6 h-6 text-green-500 sm:inline">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                                        </svg>
                                        <span class="block sm:inline">{!!$vehicle->{'label_'.$i}!!}</span>
                                    </span>
                                @endfor
                            </div>
                        </div>

                        <div class="flex items-center md:mr-10 max-w-80 text-[var(--color-accent)]">
                            <x-vehicle-image :theme="$vehicle_theme ?? 'realistic'" :type="$vehicle->type->value" />
                        </div>

                    </div>

                    <span class="flex flex-col justify-between text-right">
                        <div class="flex flex-col">

                            @if(tenant('settings')['highprice_enabled'] ?? false)
                                @php
                                    $highPrice = round($prices[$vehicle->id]['subtotal'] * (1 + tenant('settings')['highprice_percentage'] / 100));
                                @endphp
                                <span class="text-sm font-bold line-through text-zinc-700">
                                    {{ App\Helpers\Helper::strfmon($highPrice) }}
                                </span>
                            @endif
                            <span class="text-lg font-bold text-zinc-700">
                                {{ App\Helpers\Helper::strfmon($prices[$vehicle->id]['subtotal']) }}
                            </span>
                            <span class="text-sm text-gray-500">Enkele rit</span>
                        </div>
                        <span class="font-medium text-gray-900">

                            <flux:button variant="primary" type="button" class="cursor-pointer" wire:click="submit({{$vehicle->id}})">
                                <span wire:loading.delay.remove>Selecteer</span>
                                <span wire:loading.delay>Laden...</span>
                            </flux:button>

                        </span>
                    </span>
                </label>
            </div>
            @endforeach

            @if(tenant('payment')['enable_payment_method_selection'] ?? false)
            <hr>

            <div class="p-4 border rounded-lg shadow-xs">
                <h2 class="mb-4 text-lg font-bold leading-5 text-zinc-600">Betaalmethode</h2>
                <select wire:model="payment_method"
                class="block w-full py-2 pl-3 pr-10 mt-1 text-base border-gray-300 rounded-md focus:border-zinc-500 focus:outline-hidden focus:ring-zinc-500 sm:text-sm">
                    @foreach ($payment_methods as $payment_method)
                        <option value="{{$payment_method}}">{{$payment_method}}</option>
                    @endforeach
                </select>
            </div>
            @endif

        </div>

        {{-- Footer --}}
        @include('livewire.reservation-wizard.footer', ['button_text' => 'Bereken mijn ritprijs', 'wireSubmit' => true])
    </form>

</zerre>
