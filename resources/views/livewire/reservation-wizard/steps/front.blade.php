<zerre>

@if (env('APP_ENV') != 'production') @include('livewire.reservation-wizard.navigation') @endif

<div class="border rounded-lg border-zinc-200 dark:border-zinc-700">
    <div class="px-4 py-5 border-b rounded-t-lg bg-zinc-50 dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700 sm:px-6">
        <h1 class="text-xl font-bold leading-5 text-[var(--color-accent-foreground)]-900 dark:text-zinc-100"><PERSON><PERSON> eenvoudig je taxirit</h1>
    </div>

    <form wire:submit="submit(Object.fromEntries(new FormData($event.target)))">
        <div class="px-4 py-6 md:px-6">
            <div>
                @if ($errors->any())
                <div class="p-4 mb-5 rounded-md bg-red-50">
                    <div class="flex">
                        <div class="shrink-0"><svg class="w-5 h-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" /></svg></div>
                        <div class="ml-3"><h3 class="text-sm font-medium text-red-800">Oeps! Er bevinden zich fouten in het formulier.</h3></div>
                    </div>
                </div>
                @endif
            </div>

            <div>
                <ul role="list" class="block">
                    @foreach ($waypoints as $key => $waypoint)

                        {{-- If waypoints_enabled, include add-waypoint-button just before the last loop --}}
                        @includeWhen(tenant('settings')['waypoints_enabled'], 'livewire.reservation-wizard.components.add-waypoint-button')

                        <li wire:key="waypoint-item-{{$key}}">
                            <div class="relative {{$loop->last ? 'pb-2' : ($addresses ? 'pb-4' : 'pb-8' )}}">
                                @if (!$loop->last) <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-zinc-200 hidden sm:block" aria-hidden="true"></span> @endif
                                <div class="relative flex space-x-3">
                                    <span class="flex items-center justify-center hidden w-8 h-8 rounded-full bg-zinc-400 ring-8 ring-white sm:block">{!!$waypoint['icon']!!}</span>

                                    <div class="w-full">
                                        <div class="flex rounded-md">

                                            <flux:input.group class="static">

                                                <flux:input.group.prefix class="w-16">{{ $waypoint['label'] }}</flux:input.group.prefix>

                                                <flux:input type="text" name="{{$waypoint['name']}}" data-key="{{$key}}" wire:model="waypoints.{{$key}}.value" class:input="autocomplete-input">

                                                    @if($waypoint['is_waypoint'])
                                                        <x-slot name="iconTrailing"> <flux:button size="sm" variant="subtle" icon="x-mark" class="-mr-1" wire:click="removeWaypoint('{{$key}}')" /> </x-slot>
                                                    @endif

                                                </flux:input>

                                                @auth @if($this->addresses)

                                                    <flux:dropdown class="address-dropdown">
                                                        <flux:button class="cursor-pointer">
                                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin-house"><path d="M15 22a1 1 0 0 1-1-1v-4a1 1 0 0 1 .445-.832l3-2a1 1 0 0 1 1.11 0l3 2A1 1 0 0 1 22 17v4a1 1 0 0 1-1 1z"/><path d="M18 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 .601.2"/><path d="M18 22v-3"/><circle cx="10" cy="10" r="3"/></svg>
                                                            <span class="hidden sm:block">Mijn adressen</span>
                                                        </flux:button>

                                                        <flux:menu class="address-dropdown-menu">
                                                            @foreach ($this->addresses as $address)
                                                            <flux:menu.item icon="map-pin"
                                                            x-on:click="$wire.$el.querySelector('[data-key={{$key}}]').value = '{{$address->address['formatted_address']}}'; $wire.dispatch('waypoint-updated', { key:'{{$key}}', address:'{{$address->address['formatted_address']}}' });">
                                                                {{$address->address['formatted_address']}}
                                                            </flux:menu.item>
                                                            @endforeach
                                                        </flux:menu>
                                                    </flux:dropdown>

                                                @endif @endauth

                                            </flux:input.group>

                                        </div>

                                        @error($waypoint['name'])
                                            <div class="px-4 py-3 my-2 text-sm text-red-700 rounded-md bg-red-50"><span class="error">{{ $message }}</span></div>
                                        @enderror

                                    </div>

                                </div>
                            </div>
                        </li>
                    @endforeach

                </ul>

                <div class="max-w-2xl mt-6">

                    <flux:input type="datetime-local" wire:model="datetime" min="{{ now()->format('Y-m-d') }}" label="Wanneer wilt u worden opgehaald?" icon="calendar" />

                    @if($origin_is_airport)

                        <flux:input type="text" placeholder="Vluchtnummer" wire:model="flightnr" maxlength="10" class="mt-4" />

                    @endif


                    <div class="mt-6 mb-2"> <flux:checkbox wire:model.live="retour" label="Retour" /> </div>

                    @if($retour)

                        <flux:input type="datetime-local" wire:model="retour_datetime" min="{{ now()->format('Y-m-d\TH:m') }}" label="Retour datum & tijd" icon="calendar" />

                        @if($destination_is_airport)
                            <flux:input type="text" placeholder="Vluchtnummer" wire:model="retour_flightnr" maxlength="10" class="mt-4" />
                        @endif

                    @endif

                </div>

            </div>

        </div>

        @auth @if($this->addresses)
            <flux:modal name="edit-profile" class="md:w-96">
                <div class="space-y-6">
                    <div>
                        <flux:heading size="lg">Update profile</flux:heading>
                        <flux:subheading>Make changes to your personal details.</flux:subheading>
                    </div>

                    <flux:input label="Name" placeholder="Your name" />

                    <flux:input label="Date of birth" type="date" />

                    <div class="flex">
                        <flux:spacer />

                        <flux:button type="submit" variant="primary">Save changes</flux:button>
                    </div>
                </div>
            </flux:modal>
            @endif @endauth

        @include('livewire.reservation-wizard.footer', ['button_text' => 'Bereken mijn ritprijs'])
    </form>
</div>


@script
<script>

    let autocomplete = [];
    var options = { language: 'nl', types: ['establishment', 'geocode'], componentRestrictions: {country: ['nl', 'be']} };

    window.initMapsAutocomplete = function() { loopAllWaypointElements(); }

    function loopAllWaypointElements(){

        document.querySelectorAll('.autocomplete-input').forEach(function(element, index, arr) {

            // init GoogleMaps Autocomplete on all location input fields (waypoints).
            initAutocompleteForElement(element, index);

        });

    }

    function initAutocompleteForElement(element, index){

        autocomplete[index] = new google.maps.places.Autocomplete(element, options);


        google.maps.event.addListener(autocomplete[index], 'place_changed', function() {

            if(this.getPlace().formatted_address){
                var address = this.getPlace().formatted_address;
            }else{
                var address = this.getPlace().name;
            }

            Livewire.dispatch('waypoint-updated', { key:element.dataset.key, address:address });

        });

        element.addEventListener('keydown', function(e){

            if (e.keyCode == 13 && $('.pac-container:visible').length) {

                e.preventDefault();

            }

        });

    }

    Livewire.hook('commit', ({ component, commit, respond, succeed, fail }) => {
        // Equivalent of 'message.sent'

        succeed(({ snapshot, effect }) => { // Equivalent of 'message.received'

            queueMicrotask(() => { // Equivalent of 'message.processed'

                setTimeout(function() { loopAllWaypointElements(); }, 3);

            })
        })

    });

    loadMaps();




</script>
@endscript




@section('styles')
<style>
    .pac-container {
        background-color: #fff;
        position: absolute!important;
        z-index: 1000;
        /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3); */
        box-shadow:none;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        overflow: hidden;
        padding:0!important;
        border-radius: 10px;
        border:1px solid rgb(228, 228, 231);
        margin-top:4px;
    }

    @media only screen and (max-width:768px){
        .pac-container{
            width: calc(100% - 20px) !important;
            left: 10px !important;
        }
    }

    .pac-container:after {
        background-image: none !important;
        height: 0px;
        margin:0;padding:0;
    }
    .pac-item {
        display:block;
        cursor: default;
        padding:12px!important;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 30px;
        text-align: left;
        font-size: 11px;
        color: #999;
        border:none;
        border-top:1px solid #ccc;
    }
    .pac-item:first-child { border:none; }
    .pac-item:hover,
    .pac-item-selected,
    .pac-item-selected:hover{
        background-color: #4F46E5;
    }


    .pac-item:hover .pac-item-query, .pac-item:hover .pac-item-query + span,
    .pac-item-selected .pac-item-query, .pac-item-selected .pac-item-query + span,
    .pac-item-selected:hover .pac-item-query, .pac-item-selected:hover .pac-item-query + span
    {
        color:#FFF;
    }

    .pac-item-query {
        margin:0!important;
        padding:0!important;
        font-size: 13px;
        display:block;
        color: #5c5b5b;
        font-weight:800;
        font-size:15px;
        height:22px;
    }
    .pac-item-query + span{
        margin:0!important;
        padding:0!important;
        color:#5c5b5b;
        font-size:14px;
    }
    .pac-icon { display:none; }
    .pac-icon-search { background-position: -1px -1px }
    .pac-item-selected .pac-icon-search { background-position: -18px -1px }
    .pac-icon-marker { background-position: -1px -161px }
    .pac-item-selected .pac-icon-marker { background-position: -18px -161px }
    .pac-placeholder { color: zinc }

    .filled span, .filled input{background:red!important}


    /* Date & time inputs for makeing the whole input clickable for datetime selection */
    input[type="datetime-local"],
    input[type="date"],
    input[type="time"]{
        position:relative;
    }

    input[type="datetime-local"]::-webkit-calendar-picker-indicator,
    input[type="date"]::-webkit-calendar-picker-indicator,
    input[type="time"]::-webkit-calendar-picker-indicator {
        background: transparent;
        cursor: pointer;
        height: 32px;
        width:100%;
        position: absolute;
        left: 0;
    }

    @media only screen and (max-width:768px){
        .address-dropdown-menu{
            width: calc(100% - 20px) !important;
            left: 10px !important;
        }
    }

</style>
@endsection
</zerre>
