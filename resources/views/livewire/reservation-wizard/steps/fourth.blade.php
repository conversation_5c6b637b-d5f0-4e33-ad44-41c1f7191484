<zerre> @include('livewire.reservation-wizard.navigation')

<div class="border rounded-lg border-zinc-200 dark:border-zinc-700">
    <form wire:submit="submit">
        <div class="flex flex-col md:flex-row md:space-x-8">
            {{-- left side (reisdata) --}}
            <div class="border-b border-r md:border-b-0 border-zinc-200 basis-1/3">
                <div class="px-4 py-5">
                    <h3 class="text-base font-semibold leading-6 text-zinc-900">Reisgegevens</h3>
                </div>
                <div class="border-t border-zinc-200">
                    <dl>
                        <div class="grid grid-cols-2 gap-4 px-4 py-5 border-b border-zinc-200">
                            <dt class="text-sm font-medium text-zinc-500">Ritprijs</dt>
                            <dd class="mt-1 text-sm font-bold text-zinc-900"> {{ App\Helpers\Helper::strfmon( $fare ) }} </dd>

                            {{-- Extra Services/Additions --}}
                            @if ($chosenAdditionsTotal > 0)
                                @foreach ($additions as $addition)
                                    @if(@$addition['selected'])
                                        <dt class="text-sm font-medium text-zinc-500">{{ $addition['name'] }}</dt>
                                        <dd class="mt-1 text-sm font-bold text-zinc-900">
                                            {{ App\Helpers\Helper::strfmon( $addition['price'] ) }}
                                        </dd>
                                    @endif
                                @endforeach
                            @endif

                            {{-- If retour, show double price --}}
                            @if($reservation_data['retour'])
                                <dt class="text-sm font-medium text-zinc-500">+ Retour</dt>
                                <dd class="mt-1 text-sm font-bold text-zinc-900"> {{ App\Helpers\Helper::strfmon( $fare ) }} </dd>

                                {{-- Extra Services/Additions (copy from above) --}}
                                @if ($chosenAdditionsTotal > 0)
                                    @foreach ($additions as $addition)
                                        @if(@$addition['selected'])
                                            <dt class="text-sm font-medium text-zinc-500">{{ $addition['name'] }}</dt>
                                            <dd class="mt-1 text-sm font-bold text-zinc-900">
                                                {{ App\Helpers\Helper::strfmon( $addition['price'] ) }}
                                            </dd>
                                        @endif
                                    @endforeach
                                @endif

                            @endif

                            {{-- Totaal --}}
                            @if($chosenAdditionsTotal > 0 || $reservation_data['retour'])
                                <dt class="text-sm font-medium text-zinc-500">Totaal</dt>
                                <dd class="mt-1 text-sm font-bold text-zinc-900">
                                    {{ App\Helpers\Helper::strfmon( $price ) }}
                                </dd>
                            @endif
                        </div>



                        <div class="grid grid-cols-2 gap-4 px-4 py-5 border-b border-zinc-200">
                            <dt class="text-sm font-medium text-zinc-500">Voertuig</dt>
                            <dd class="mt-1 text-sm font-bold text-zinc-900">{{$chosen_vehicle_name}}</dd>
                        </div>
                        <div class="grid grid-cols-2 gap-4 px-4 py-5 border-b border-zinc-200">
                            <dt class="text-sm font-medium text-zinc-500">Ophaalmoment</dt>
                            <dd class="mt-1 text-sm font-bold text-zinc-900">{{\Carbon\Carbon::parse($reservation_data['datetime'])->format('d.m.Y H:i')}}</dd>
                        </div>

                        @if(!empty($reservation_data['flightnr']))
                            <div class="grid grid-cols-2 gap-4 px-4 py-5 border-b border-zinc-200">
                                <dt class="text-sm font-medium text-zinc-500">Vluchtnummer</dt>
                                <dd class="mt-1 text-sm font-bold text-zinc-900">{{ $reservation_data['flightnr'] }}</dd>
                            </div>
                        @endif

                        @if($reservation_data['retour'])

                            <div class="grid grid-cols-2 gap-4 px-4 py-5 border-b border-zinc-200">
                                <dt class="text-sm font-medium text-zinc-500">Retour ophaalmoment</dt>
                                <dd class="mt-1 text-sm font-bold text-zinc-900">{{\Carbon\Carbon::parse($reservation_data['retour_datetime'])->format('d.m.Y H:i')}}</dd>
                            </div>

                            @if(!empty($reservation_data['retour_flightnr']))
                                <div class="grid grid-cols-2 gap-4 px-4 py-5 border-b border-zinc-200">
                                    <dt class="text-sm font-medium text-zinc-500">Vluchtnummer</dt>
                                    <dd class="mt-1 text-sm font-bold text-zinc-900">{{ $reservation_data['retour_flightnr'] }}</dd>
                                </div>
                            @endif

                        @endif

                        <div class="grid grid-cols-2 gap-4 px-4 py-5 bg-white">
                            <dt class="text-sm font-medium text-zinc-500">Reis</dt>
                            <dd class="mt-2 text-sm text-zinc-900 md:mt-0">
                                <ul role="list">
                                    @foreach ($waypoint_data as $wpitem)
                                    <li>
                                        <div class="relative @if(!$loop->last) pb-6 @endif">

                                            @if(!$loop->last) <span class="absolute top-3 left-3 -ml-px h-full w-0.5 bg-zinc-200" aria-hidden="true"></span> @endif

                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="flex items-center justify-center w-6 h-6 rounded-full bg-zinc-400 ring-8 ring-white">
                                                        @if ($loop->last)
                                                        <span class="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full ring-8 ring-white">
                                                            <svg class="w-5 h-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                                <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                        @else
                                                        <span class="flex items-center justify-center w-6 h-6 rounded-full bg-zinc-400 ring-8 ring-white">
                                                            <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                                                            </svg>
                                                        </span>
                                                        @endif
                                                    </span>
                                                </div>
                                                <div class="flex justify-between flex-1 min-w-0 space-x-4">
                                                    <div>
                                                        <p class="text-sm font-bold text-zinc-700">
                                                            @if( $wpitem['type'] == 'premise' || $wpitem['type'] == 'route' || $wpitem['type'] == 'street_address') {{$wpitem['route']}} {{$wpitem['street_number'] ?? ''}}
                                                            @else {{$wpitem['airport'] ?? ''}}
                                                            @endif
                                                        </p>
                                                        <p class="text-sm text-zinc-500">{{$wpitem['postal_code']}} {{$wpitem['locality']}} </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </dd>

                            {{-- If retour, same code but reverse. --}}


                        </div>
                    </dl>
                </div>
            </div>

            {{-- right side (form) --}}
            <div class="p-8 space-y-4 basis-2/3">

                @if ($errors->any())
                    <div class="p-4 rounded-md bg-red-50">
                        <div class="flex">
                            <div class="shrink-0"><svg class="w-5 h-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" /></svg></div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Oeps! Er bevinden zich fouten in het formulier.</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul role="list" class="pl-5 space-y-1 list-disc">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div>
                    <label for="name" class="block text-sm font-medium text-zinc-700">Naam</label>
                    <div class="mt-1">
                        <input type="text" wire:model.blur="name" class="flex-1 block w-full rounded-md border-zinc-300 focus:border-zinc-500 focus:ring-zinc-500 sm:text-sm">
                        @error('name')
                        <div class="px-4 py-3 my-2 text-sm text-red-700 rounded-md bg-red-50"><span class="error">{{ $errors->first('name') }}</span></div>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="phone" class="block text-sm font-medium text-zinc-700">Telefoonnummer</label>
                    <div class="mt-1">

                        @if (tenant('settings')['intl_phone_enabled'] ?? false)

                            <livewire:telinput :phone="$phone ?? ''" />

                        @else

                            <input type="tel" wire:model.blur="phone" class="flex-1 block w-full rounded-md border-zinc-300 focus:border-zinc-500 focus:ring-zinc-500 sm:text-sm">

                        @endif

                        @error('phone')

                            <div class="px-4 py-3 my-2 text-sm text-red-700 rounded-md bg-red-50"><span class="error">{{ $errors->first('phone') }}</span></div>

                        @enderror
                    </div>
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-zinc-700">E-mailadres</label>
                    <div class="mt-1">
                        <input type="email" wire:model.blur="email" class="flex-1 block w-full rounded-md border-zinc-300 focus:border-zinc-500 focus:ring-zinc-500 sm:text-sm">
                        @error('email')
                        <div class="px-4 py-3 my-2 text-sm text-red-700 rounded-md bg-red-50"><span class="error">{{ $errors->first('email') }}</span></div>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="comments" class="block text-sm font-medium text-zinc-700">Opmerkingen <span class="ml-1 text-sm text-zinc-400">optioneel</span></label>
                    <div class="mt-1">
                        <textarea wire:model.blur="comments" rows="3" class="block w-full rounded-md shadow-xs border-zinc-300 focus:border-zinc-500 focus:ring-zinc-500 sm:text-sm"></textarea>
                    </div>
                </div>

                @if (!empty($additions))
                    @include('livewire.reservation-wizard.components.additions', compact('additions'))
                @endif
                <div>

                </div>

                {{-- <div>
                    <label for="password" class="block text-sm font-medium text-zinc-700">Maak een wachtwoord aan <span class="ml-1 text-sm text-zinc-400">optioneel</span></label>
                    <div class="mt-1">
                        <input type="password" wire:model="password" class="flex-1 block w-full rounded-md border-zinc-300 focus:border-zinc-500 focus:ring-zinc-500 sm:text-sm">
                    </div>
                </div> --}}

            </div>
        </div>

        {{--
            if online payment is enabled, and there is an api key.
            Footer
            I'm using two different methods for submitting the form, because of targeting the loader indicator.
            The loading... indicator can only be targeted by methodName.
            Otherwise I'd have used submit(withPayment:true/false) with withPayment argument.
        --}}

        <div class="flex flex-row justify-end gap-3 p-4 border-t rounded-b-lg bg-zinc-50 place-items-end border-zinc-200">

            @if (!$onlinePaymentRequired)
                <flux:button variant="filled" type="button" wire:click="submit(false)" class="cursor-pointer">
                    <span wire:loading.delay.remove wire:target="submit(false)"> Reserveren @if ($showOnlinePayment) zonder te betalen @endif </span>
                    <span wire:loading.delay wire:target="submit(false)">Laden...</span>
                </flux:button>
            @endif

            @if ($showOnlinePayment)
                <flux:button variant="primary" type="button" wire:click="submit(true)" class="bg-green-600 cursor-pointer hover:bg-green-700">
                    <span wire:loading.delay.remove class="font-bold" wire:target="submit(true)">
                        Online betalen
                    </span>
                    <span wire:loading.delay wire:target="submit(true)">Laden...</span>
                </flux:button>
            @endif

            @error('payment')
                <div class="px-4 py-3 my-2 text-sm text-red-700 rounded-md bg-red-50">
                    <span class="error">{{ $message }}</span>
                </div>
            @enderror

            @script <script> $wire.on('scroll-to-top', () => { scrollToTop(); }); </script> @endscript

        </div>

    </form>
</div>
</zerre>
