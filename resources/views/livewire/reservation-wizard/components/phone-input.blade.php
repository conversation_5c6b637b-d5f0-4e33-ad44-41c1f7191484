<div>
    <div
        x-data="phoneInputComponent"
        x-init="init()"
        wire:ignore
        class="phone-input-container"
    >

        <input type="tel" x-ref="phoneInput" id="phoneInput" placeholder="{{ $placeholder }}" {{ $required ? 'required' : '' }} class="form-control" />
        <input type="hidden" name="phoneInput" value="{{ $phone }}" wire:model="phone" />

    </div>

    {{-- Include assets --}}
    @once
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@latest/build/css/intlTelInput.css">
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@latest/build/js/intlTelInput.min.js"></script>
    <style>
        .phone-input-container {
            width: 100%;
        }
        .phone-input-container .iti {
            width: 100%;
        }
        .phone-input-container .iti__flag-container {
            z-index: 999;
        }
    </style>
    @endonce

    {{-- Component JavaScript with Livewire v3 hooks --}}
    @script
    <script>
        window.phoneInputComponent = {
            phoneInput: null,
            detectedCountry: '{{ $initialCountry }}',

            async init() {
                // Try to detect country client-side if enabled
                @if($autoDetectCountry)
                    await this.detectCountry();
                @endif

                await this.setupIntlTelInput();
                this.setupEventListeners();
            },

            async detectCountry() {
                try {
                    // Option 1: Use ipinfo.io which has CORS enabled
                    const response = await fetch('https://ipinfo.io/json?token=demo');
                    const data = await response.json();
                    if (data.country) {
                        this.detectedCountry = data.country.toLowerCase();
                        return;
                    }
                } catch (error) {
                    console.log('Primary detection failed, trying alternative');
                }

                try {
                    // Option 2: Use ip-api.com which supports CORS (but rate limited)
                    const response = await fetch('https://ipapi.co/json/', {
                        mode: 'no-cors' // This won't give us the response but at least won't error
                    });
                    // Since no-cors doesn't give us response, we'll use a JSONP approach
                    await this.detectCountryJsonp();
                } catch (error) {
                    console.log('Country detection failed, using default');
                }
            },

            detectCountryJsonp() {
                return new Promise((resolve) => {
                    // Create a unique callback name
                    const callbackName = 'phoneInputCallback' + Date.now();

                    // Create the callback function
                    window[callbackName] = (data) => {
                        if (data && data.country_code) {
                            this.detectedCountry = data.country_code.toLowerCase();
                        }
                        // Clean up
                        delete window[callbackName];
                        document.querySelector(`script[data-callback="${callbackName}"]`).remove();
                        resolve();
                    };

                    // Create script tag for JSONP
                    const script = document.createElement('script');
                    script.src = `https://ipapi.co/jsonp?callback=${callbackName}`;
                    script.dataset.callback = callbackName;
                    script.onerror = () => {
                        delete window[callbackName];
                        resolve();
                    };

                    document.head.appendChild(script);

                    // Timeout after 2 seconds
                    setTimeout(() => {
                        if (window[callbackName]) {
                            delete window[callbackName];
                            script.remove();
                            resolve();
                        }
                    }, 2000);
                });
            },

            async setupIntlTelInput() {
                // Wait for intlTelInput to be available
                while (typeof window.intlTelInput === 'undefined') {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                this.phoneInput = window.intlTelInput(this.$refs.phoneInput, {
                    initialCountry: this.detectedCountry,
                    preferredCountries: ['us', 'gb', 'ca', 'au'],
                    utilsScript: 'https://cdn.jsdelivr.net/npm/intl-tel-input@latest/build/js/utils.js'
                });

                // Set initial value if provided
                const initialPhone = @js($phone);
                if (initialPhone) {
                    this.phoneInput.setNumber(initialPhone);
                }
            },

            setupEventListeners() {

                // Update Livewire when phone number changes
                this.$refs.phoneInput.addEventListener('input', () => {
                    this.updatePhone();
                });

                // Listen for country change
                this.$refs.phoneInput.addEventListener('countrychange', () => {
                    this.updatePhone();
                });

            },

            updatePhone() {

                var fullNumber = this.phoneInput.getNumber();

                $wire.set('phone', fullNumber);

                // Dispatch Alpine event with proper detail structure
                // this.$dispatch('phone-updated', { phone: fullNumber });

                // Dispatch Livewire event - pass the data directly, not in an object
                $wire.dispatch('phone-updated', { phone: fullNumber });

            }
        };
    </script>
    @endscript
</div>
