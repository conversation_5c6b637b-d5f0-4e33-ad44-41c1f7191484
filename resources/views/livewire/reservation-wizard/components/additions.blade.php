<p class="block mb-1 text-sm font-medium text-zinc-700">Extra's</p>
<div class="space-y-2">
    @foreach ($additions as $addition)
    <label for="addition_{{ $addition['id'] }}" class="flex gap-2">
        <div class="flex items-center h-5 shrink-0">
          <div class="grid grid-cols-1 group size-4">
            <input id="addition_{{ $addition['id'] }}" aria-describedby="addition_{{ $addition['id'] }}_description" name="addition_{{ $addition['id'] }}" type="checkbox" class="col-start-1 row-start-1 bg-white border border-gray-300 rounded-sm appearance-none checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"
            wire:model.live="additions.{{$addition['id']}}.selected">
            <svg class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-disabled:stroke-gray-950/25" viewBox="0 0 14 14" fill="none">
              <path class="opacity-0 group-has-checked:opacity-100" d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              <path class="opacity-0 group-has-indeterminate:opacity-100" d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </div>
        </div>
        <div>
          <p class="text-sm font-medium text-zinc-700">{{ $addition['name'] }} + {{ \App\Helpers\Helper::strfmon($addition['price']) }}</p>
          @if (!empty($addition['description']))
            <p id="addition_{{ $addition['id'] }}_description" class="text-gray-500">{{ $addition['description'] }}</p>
          @endif
        </div>
    </label>
    @endforeach
</div>
