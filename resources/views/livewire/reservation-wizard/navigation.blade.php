<div class="pb-4 md:pb-6 lg:pb-8">
    <nav aria-label="Steps" class="bg-white">
        <ol role="list" class="grid grid-cols-4 border rounded-md divide-zinc-200 border-zinc-200">

            @foreach($steps as $key => $step)
            
            <li class="relative md:flex md:flex-1 rounded-l-md {{$loop->last?'':'border-r'}} border-zinc-200 {{$step->isPrevious()? 'bg-green-50':''}} {{$step->isCurrent()?'ring-2 rounded-sm ring-[var(--color-accent)]':''}}">
                
                <a class="group w-full flex items-center {{$step->isPrevious() || $step->isCurrent() ? 'cursor-pointer' : 'cursor-not-allowed'}}"
                    
                    @if($loop->first) wire:click="forgetState" @endif
                    
                    @if(!$loop->first && $step->isPrevious()) wire:click="{{ $step->show() }}" @endif
                    
                    {{$step->isPrevious() ? 'x-description="Completed Step"' : '' }}
                    
                    {{$step->isCurrent() ? 'aria-current="step" x-description="Current Step"' : '' }}
                    
                    {{$step->isNext() ? 'x-description="Upcoming Step"' : '' }}
                    
                    >
                    <span class="flex items-center px-2 py-4 text-sm font-medium grow lg:px-4">

                        <svg viewBox="0 0 24 24" fill="currentColor"
                        class="w-6 h-6 grow md:grow-0
                            {{$step->isCurrent() ? 'text-[var(--color-accent)]' : ($step->isPrevious() ? 'text-green-600' : 'text-gray-300 group-hover:text-gray-400')}}
                        ">
                            @if ($step->isPrevious()) {{-- CheckMark Icon --}}
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z" clip-rule="evenodd" />
                            @else
                                {!! $step->icon !!}
                            @endif
                        </svg>

                        <span class="ml-2 text-sm font-medium {{$step->isCurrent() ? 'text-[var(--color-accent)]' : ($step->isPrevious()?'text-green-800':'text-gray-300 group-hover:text-gray-400') }} hidden md:block">
                            {{ $step->label }}
                        </span>
                        
                    </span>
                </a>
                    
            </li>
            @endforeach
                
        </ol>
    </nav>  
</div>