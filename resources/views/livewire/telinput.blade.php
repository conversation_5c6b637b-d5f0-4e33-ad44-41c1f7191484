<?php

use Livewire\Volt\Component;

new class extends Component {
    public string $phone; // To use for initial value
    public string $validationError = '';
}; ?>

<div wire:ignore>

    <input type="tel" id="telInput" required class="flex-1 block w-full rounded-md border-zinc-300 focus:border-zinc-500 focus:ring-zinc-500 sm:text-sm" />
    <style>
        .iti {width: 100%!important;}
        .iti input.iti__tel-input, .iti input.iti__tel-input[type=text], .iti input.iti__tel-input[type=tel] {padding-left:48px!important;}
    </style>

    @assets
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@latest/build/js/intlTelInput.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@latest/build/css/intlTelInput.css" defer>
    @endassets

    @script
    <script>
        // This I found was needed too when doing something with GoogleMaps Js API in Livewire.
        setTimeout(initi, 1);

        function initi(){
            window.input = document.querySelector("#telInput");
            window.iti = intlTelInput(input, {
                loadUtils: () => import("https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"),
                initialCountry: "auto",
                geoIpLookup: (success, failure) => {
                    fetch("https://ipapi.co/json")
                    .then((res) => res.json())
                    .then((data) => success(data.country_code))
                    .catch(() => failure());
                }
            });

            if("{{ $phone }}") iti.setNumber("{{ $phone }}");

            iti.promise.then(() => {

                // Listen for changes
                input.addEventListener("change", updateHandler, false); // input=live, change=blur
                input.addEventListener("countrychange", updateHandler, false);

            });

        }

        function updateHandler() {

            const isValid = iti.isValidNumber();

            // if input is not empty, validate.
            if(input.value && !isValid) {

                const error = iti.getValidationError(); // int (Errno)
                const errorMessage = getError(error); // string (Message)

                $wire.set('validationError', errorMessage);
                $wire.dispatch('phone-validation-error-updated', { message: errorMessage });

            } else {

                $wire.set('validationError', '');

                // Only possible when number isValid.
                $wire.dispatch('phone-updated', { phone: iti.getNumber() });

            }
        }

        function getError(error) {
            switch (error) {
                case intlTelInput.utils.validationError.TOO_SHORT: //2
                    return "The phone number is too short for the selected country";
                case intlTelInput.utils.validationError.TOO_LONG: //3
                    return "The phone number is too long for the selected country";
                case intlTelInput.utils.validationError.IS_POSSIBLE_LOCAL_ONLY: //4
                    return "The phone number is local only";
                case intlTelInput.utils.validationError.INVALID_COUNTRY_CODE: //1
                    return "The selected country code is invalid";
                case intlTelInput.utils.validationError.INVALID_LENGTH: //5
                    return "The phone number is of inValid length";
                default:
                    return "There is something wrong with the phone number";
            }
        }
    </script>
    @endscript

</div>
