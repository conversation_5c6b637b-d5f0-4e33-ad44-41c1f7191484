<zerre>
    <h1>Test</h1>
    <br>
    <div id="autocomplete"></div>
    <div id="secondElement"></div>

    {{-- Google Maps --}}
    <script>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
        ({key: "AIzaSyDM8V6QQhHWLfmAY_D6vzp_E70glb9Fax8", v: "weekly"});</script>

    {{-- autocompletejs --}}
    <script src="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-js"></script>
    <link
    rel="stylesheet"
    href="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-theme-classic@1.19.2/dist/theme.min.css"
    integrity="sha256-4Wtj6dqgMBT/Ji+vI49GON0NbfDlaJH06SUD7TH4yYg="
    crossorigin="anonymous"
    />
@script
<script>
const { autocomplete } = window['@algolia/autocomplete-js'];

async function getAutocompleteSuggestions(request) {

    const { Place, AutocompleteSessionToken, AutocompleteSuggestion } = await google.maps.importLibrary("places");
    const token = new AutocompleteSessionToken();
    request.sessionToken = token;

    const { suggestions } = await AutocompleteSuggestion.fetchAutocompleteSuggestions(request);

    if ( suggestions ) {
        mappedSuggestions = suggestions.map(suggestion => {
            return {
                text: suggestion.placePrediction.text.text,
                place_id: suggestion.placePrediction.placeId
            };
        });
    }
    return mappedSuggestions;

}

async function geoFindMe() {
    if (!navigator.geolocation) {
        throw new Error("Geolocation is not supported by your browser");
    }

    return new Promise((resolve, reject) => {
        const options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0,
        };

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const latitude = position.coords.latitude;
                const longitude = position.coords.longitude;
                resolve({ latitude, longitude });
            },
            (err) => {
                reject(new Error(`Unable to retrieve your location: ERROR(${err.code}): ${err.message}`));
            },
            options
        );
    });
}

autocomplete({
    debug:true,
    container: '#autocomplete',
    placeholder: 'Zoek naar uw adres...',
    openOnFocus: true,
    getSources({ query }) {
        // If query is empty, return currentLocation and saved addresses,
        // otherwise return Google Maps
        if(query.length === 0){
            return [

                // Current location
                {
                    sourceId: 'currentLocation',
                    getItems() {
                        return [ {text: 'Huidige locatie', id: 'currentLocation'} ];
                    },
                    templates: {
                        header({html}) { return html`<b>Huidige Locatie</b>`; },
                        item({ item, components, html }) {
                            return html`<div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" style="display:inline;height:22px;width:22px;margin-right:8px;"><rect width="256" height="256" fill="none"/><line x1="128" y1="240" x2="128" y2="208" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><circle cx="128" cy="128" r="80" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><line x1="128" y1="16" x2="128" y2="48" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><line x1="16" y1="128" x2="48" y2="128" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><line x1="240" y1="128" x2="208" y2="128" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><circle cx="128" cy="128" r="32" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/></svg>
                                ${item.text}
                            </div>`;
                        }
                    },
                    onSelect({ item, setQuery, event     }) {

                        event.preventDefault();

                        (async () => {
                            try {
                                const geo = await geoFindMe();
                                setQuery(item.text + ': ' + geo.latitude + ', ' + geo.longitude);
                                // console.log("Coordinates:", geo);
                            } catch (error) {
                                console.error("Error getting geolocation:", error.message);
                            }
                        })();

                    },
                }, // End current location source

                // IF there are saved addresses, second source should be all these saved addresses.
                @if (auth()->user()->addresses->count() > 0)
                // Saved addresses
                {
                    sourceId: 'savedAddresses',
                    getItems() {
                        return [
                            @foreach (auth()->user()->addresses as $address )
                                {text: '{{ $address->address['formatted_address'] }}', id: '{{ $address->id }}'},
                            @endforeach
                        ];
                    },
                    templates: {
                        header({html}) { return html`<b>Opgeslagen adressen</b>`; },
                        item({ item, components, html }) {
                            return html`<div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" style="display:inline;height:22px;width:22px;margin-right:8px;"><rect width="256" height="256" fill="none"/><line x1="128" y1="240" x2="128" y2="208" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><circle cx="128" cy="128" r="80" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><line x1="128" y1="16" x2="128" y2="48" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><line x1="16" y1="128" x2="48" y2="128" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><line x1="240" y1="128" x2="208" y2="128" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/><circle cx="128" cy="128" r="32" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"/></svg>
                                ${item.text}
                            </div>`;
                        }
                    },
                    onSelect({ item, setQuery, event }) {
                        setQuery(item.text);
                        event.preventDefault();
                    },
                },
                @endif

            ];
        }
        else{
            return [
                // Google Maps
                {
                    sourceId: 'places',
                    getItems() {
                        let result = [];
                        if(query.length > 0){
                            result = getAutocompleteSuggestions({
                                input: query,
                                locationRestriction: {
                                west: -5.15,
                                north: 55.10,
                                east: 15.50,
                                south: 42.30,
                                },
                                origin: { lat: 52.3105, lng: 4.7683 }, // Schiphol Airport
                                language: "nl-NL",
                                region: "eu",
                            });
                        }
                        return result;
                    },
                    templates: {
                        // header({html}) { return html`<b>Adressen</b>`; },
                        item({ item, components, html }) {
                        return html`<div>${item.text}</div>`;
                        },
                    },
                    onSelect({ item, setQuery, event }) { setQuery(item.text); },
                } // End Google Maps source
            ];
        }
  }, // end GetSources
  onSubmit() {return false;},
});

</script>
@endscript

</zerre>
