<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- 1. Google Maps Bootstrap Loader is placed first in the head -->
    <script>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
        ({key: "AIzaSyDM8V6QQhHWLfmAY_D6vzp_E70glb9Fax8", v: "weekly"});</script>

    <script src="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-js"></script>
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-theme-classic@1.19.2/dist/theme.min.css"
  integrity="sha256-4Wtj6dqgMBT/Ji+vI49GON0NbfDlaJH06SUD7TH4yYg="
  crossorigin="anonymous"
/>

</head>

<body class="bg-gray-100">
<zerre>
    <h1>Test</h1>
    <br>
    <div id="autocomplete"></div>




<script>
const { autocomplete } = window['@algolia/autocomplete-js'];

async function getAutocompleteSuggestions(request) {

    const { Place, AutocompleteSessionToken, AutocompleteSuggestion } = await google.maps.importLibrary("places");
    const token = new AutocompleteSessionToken();
    request.sessionToken = token;

    const { suggestions } = await AutocompleteSuggestion.fetchAutocompleteSuggestions(request);

    if ( suggestions ) {
        mappedSuggestions = suggestions.map(suggestion => {
            return {
                text: suggestion.placePrediction.text.text,
                place_id: suggestion.placePrediction.placeId
            };
        });
    }
    return mappedSuggestions;

}

autocomplete({
    debug:true,
  container: '#autocomplete',
  placeholder: 'Vind uw adres',
  getSources({ query }) {
    return [
      {
        sourceId: 'places',
        getItems() {
          return getAutocompleteSuggestions({
                input: query,
                locationRestriction: {
                west: -5.15,
                north: 55.10,
                east: 15.50,
                south: 42.30,
                },
                origin: { lat: 52.3105, lng: 4.7683 }, // Schiphol Airport
                language: "nl-NL",
                region: "eu",
            });
        },
        templates: {
          item({ item, components, html }) {
            return html`<div class="aa-ItemWrapper">
              <div class="aa-ItemContent">
                <div class="aa-ItemIcon aa-ItemIcon--alignTop">
                  <img
                    src="${item.image}"
                    alt="${item.name}"
                    width="40"
                    height="40"
                  />
                </div>
                <div class="aa-ItemContentBody">
                  <div class="aa-ItemContentTitle">
                    ${item.text}
                  </div>
                </div>
              </div>
            </div>`;
          },
        },
      }, // End return
    ];
  },
  onSubmit({ state }) {
    console.log(state);
  },
});

</script>

</zerre>

</body>
</html>
