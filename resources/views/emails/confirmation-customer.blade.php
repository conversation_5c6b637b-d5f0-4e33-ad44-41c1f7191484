<h1 class="text-[var(--color-accent-foreground)]-900" style="text-align:center;font-size:18px">
    Bedankt voor uw boeking bij
    <a href="{{ config('app.url') }}">{{ tenant('settings')['sitename'] }}</a>
</h1>

<br>

<p style="font-weight:bold;">Uw ontvangt deze email als bevestiging voor uw boeking.</p>
<p style="font-weight:bold;">Reserveringsnummer: <span>{{ $reservation->rand_id }}</span></p>
<div style="width:100%;">
    <div style="padding-top:12px;">
        <table style="width:100%;display:block;">

            @php
                $has_waypoints = (count($reservation->data['waypoints']) > 2) ? true : false;
            @endphp

            @foreach ($reservation->data['waypoints'] as $waypoint)
                <tr>
                    <td style="min-width:120px">
                        @if ($loop->first) Van:
                        @elseif ( !$loop->first && !$loop->last && $has_waypoints) Tussenstop:
                        @elseif ($loop->last) Bestemming:
                        @endif
                    </td>
                    <td style="min-width:180px">
                        @if( !empty($waypoint['airport']) )
                            {{ $waypoint['airport'] }}
                        @else
                            {{ $waypoint['route'] ?? '' }}
                            {{ $waypoint['street_number'] ?? '' }},
                            {{ $waypoint['locality'] }}
                        @endif
                    </td>
                </tr>
            @endforeach

            <tr><td style="min-width:120px">Ophaalmoment:</td><td>{{ (new DateTime($reservation->datetime))->format('d.m.Y H:i') }}</td></tr>
            @if ($reservation->data['flightnr'])
                <tr><td style="min-width:120px">Vluchtnr:</td><td>{{ $reservation->data['flightnr'] }}</td></tr>
            @endif
            <tr><td style="min-width:120px">Vervoer:</td><td>{{ $reservation->data['vehicle_name'] }}</td></tr>
            <tr><td style="min-width:120px">Aantal personen:</td><td>{{ $reservation->data['people'] }}</td></tr>
            <tr><td style="min-width:120px">Handbagage:</td><td>{{ $reservation->data['handluggage'] }}</td></tr>
            <tr><td style="min-width:120px">Koffers:</td><td>{{ $reservation->data['luggage'] }}</td></tr>

            @if (!empty($reservation->data['payment_method']))
            <tr><td style="min-width:120px">Betaalmethode:</td><td>{{ $reservation->data['payment_method']=='mollie' ? 'Online betaald': $reservation->data['payment_method'] }}</td></tr>
            @endif

            <tr><td style="min-width:120px">Prijs:</td><td>{{ \App\Helpers\Helper::strfmon($reservation->data['price']) }}</td></tr>

            @if ( !empty($reservation->data['comment']) )
                <tr><td style="min-width:120px">Opmerkingen:</td><td>{{ $reservation->data['comment'] }}</td></tr>
            @endif

            @if ( !empty($reservation->data['retour_id']) )
                <tr><td colspan="2"> <br><strong>Retour</strong> </td></tr>
                <tr><td style="min-width:120px">Retour ophaalmoment:</td><td>{{ (new DateTime($retour->datetime))->format('d.m.Y H:i') }}</td></tr>
                @if ( !empty($retour->data['flightnr']) )
                    <tr><td style="min-width:120px">Retour vluchtnr:</td><td>{{ $retour->data['flightnr'] }}</td></tr>
                @endif
                <tr><td style="min-width:120px">Totaalprijs:</td><td>{{ \App\Helpers\Helper::strfmon($reservation->data['price']) }}</td></tr>
            @endif
            <tr>
                <td style="min-width:120px;padding-top:20px;" colspan="2">

                    Vertrek naar Schiphol Airport: <br>
                    •   Zorg ervoor dat u op het gereserveerde tijdstip klaar staat.<br><br>
                    •   Wijzigingen kunnen tot 4 uur voor de reservering telefonisch doorgegeven worden.<br><br>
                    •   Indien de chauffeur langer dan 5 minuten staat te wachten zal er een wachttarief in rekening worden gebracht.<br><br>

                    Ophalen vanaf Schiphol Airport:<br>
                    1) Als u bent geland op Schiphol Airport, voor u de koffers van de band haalt, belt u naar de centrale.<br>
                    Telefoon: <a href="tel:{{ tenant('contact')['phonelink'] }}">{{ tenant('contact')['phone'] }}</a>.<br>
                    WhatsApp: <a href="tel:{{ tenant('contact')['walink'] }}">{{ tenant('contact')['wa'] }}</a>.<br>

                    <br><br>
                    2) Zodra u de bagage van de band heeft gehaald, loopt u richting vertrekhal 3 (deur C). Daar loopt u naar buiten en steekt u het zebrapad over.<br><br>
                    3) De chauffeur die u komt ophalen neemt d.m.v. telefonisch contact (SMS of bellen) contact met u op. Die zal u het merk, kenteken en kleur van de auto doorgeven.<br><br>
                    <br>
                    @if ( !empty($reservation->data['retour_id']) )
                        Let op: de gegeven prijs is voor een enkele reis.<br>
                    @endif
                </td>
            </tr>
        </table>

        <p style="width:100%">
            Met vriendelijke groet,<br>
            Team <a href="{{ config('app.url') }}">{{ tenant('settings')['sitename'] }}</a>
        </p>
    </div>
</div>
