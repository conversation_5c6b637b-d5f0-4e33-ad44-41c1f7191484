<div style="width:100%;">
    <div style="padding-top:12px;">
        <table style="width:100%;display:block;">
            {{-- If payment status is paid, then show payment method. --}}
            @if (!empty($reservation->data['payment_status']) && $reservation->data['payment_status'] == 'paid')
                <tr><td style="min-width:120px;font-weight:bold;">ONLINE BETAALD!</td></tr>
            @endif

            <tr><td style="font-weight:bold">Reservering: </td><td style="font-weight:bold">{{ $reservation->rand_id }}</td></tr>

            <tr><td colspan="2"><strong>Klantgegevens</strong></td></tr>
            <tr><td style="min-width:120px">Naam:</td><td>{{ $reservation->data['user']['name'] }}</td></tr>
            <tr><td style="min-width:120px">Telefoon:</td><td>{{ $reservation->data['user']['phone'] }}</td></tr>
            <tr><td style="min-width:120px">Email:</td><td>{{ $reservation->data['user']['email'] }}</td></tr>

            <tr><td colspan="2" style="padding-top:20px"><strong>Reisgegevens</strong></td></tr>
            <tr><td style="min-width:120px">Ophaalmoment:</td><td>{{ (new DateTime($reservation->datetime))->format('d.m.Y H:i') }}</td></tr>
            @if ($reservation->data['flightnr'])
                <tr><td style="min-width:120px">Vluchtnr:</td><td>{{ $reservation->data['flightnr'] }}</td></tr>
            @endif

            @php
                $has_waypoints = (count($reservation->data['waypoints']) > 2) ? true : false;
            @endphp

            @foreach ($reservation->data['waypoints'] as $waypoint)
                <tr>
                    <td style="min-width:120px">
                        @if ($loop->first) Van:
                        @elseif ( !$loop->first && !$loop->last && $has_waypoints) Tussenstop:
                        @elseif ($loop->last) Bestemming:
                        @endif
                    </td>
                    <td style="min-width:180px">
                        @if( !empty($waypoint['airport']) )
                            {{ $waypoint['airport'] }}
                        @else
                            {{ $waypoint['route'] ?? '' }}
                            {{ $waypoint['street_number'] ?? '' }},
                            {{ $waypoint['locality'] }}
                        @endif
                    </td>
                </tr>
            @endforeach

            <tr><td style="min-width:120px">Vervoer:</td><td>{{ $reservation->data['vehicle_name'] }}</td></tr>
            <tr><td style="min-width:120px">Aantal personen:</td><td>{{ $reservation->data['people'] }}</td></tr>
            <tr><td style="min-width:120px">Handbagage:</td><td>{{ $reservation->data['handluggage'] }}</td></tr>
            <tr><td style="min-width:120px">Koffers:</td><td>{{ $reservation->data['luggage'] }}</td></tr>

            @if ( !empty($reservation->data['payment_method']) )
                <tr><td style="min-width:120px">Betaalmethode:</td><td>{{ $reservation->data['payment_method'] }}</td></tr>
            @endif

            @if ( !empty($reservation->data['comment']) )
                <tr><td style="min-width:120px">Opmerkingen:</td><td>{{ $reservation->data['comment'] }}</td></tr>
            @endif

            @if ( !empty($reservation->data['retour_id']) )
                <tr><td colspan="2"> <br><strong>Retour</strong> </td></tr>
                <tr><td style="min-width:120px">Retour ophaalmoment:</td><td>{{ (new DateTime($retour->datetime))->format('d.m.Y H:i') }}</td></tr>
                @if ($retour->data['flightnr'])
                    <tr><td style="min-width:120px">Retour vluchtnr:</td><td>{{ $retour->data['flightnr'] }}</td></tr>
                @endif
            @endif

            <tr><td style="min-width:120px">Afstand:</td><td>{{ ceil($reservation->data['distance']) }} km</td></tr>
            <tr><td style="min-width:120px">Duratie:</td><td>{{ ceil($reservation->data['duration']) }} min.</td></tr>
            <tr><td style="min-width:120px">Prijs:</td><td>{{ \App\Helpers\Helper::strfmon($reservation->data['price']) }}</td></tr>
        </table>

        <p style="width:100%">
            Met vriendelijke groet,<br>
            Team {{ tenant('settings')['sitename'] }}
        </p>
    </div>
</div>
