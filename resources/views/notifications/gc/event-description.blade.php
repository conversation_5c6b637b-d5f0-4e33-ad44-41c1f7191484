{{-- If payment status is paid, then show payment method. --}}
@if (!empty($data['payment_status']) && $data['payment_status'] == 'paid')
ONLINE BETAALD!

@endif
@foreach ($data['waypoints'] as $key => $waypoint)
<b>{{ $key === array_key_first($data['waypoints']) ? 'Van:' : ($key === array_key_last($data['waypoints']) ? 'Naar:' : 'Tussenstop:')}}</b> {{ \App\Helpers\Helper::formatAddressForEvent($waypoint) }}
@endforeach
-
<b>Reservering:</b> {{ $rand_id }}
<b>Datumtijd:</b> {{ (new DateTime($datetime))->format('d.m.Y H:i') }}
<b>Prijs:</b> {{ \App\Helpers\Helper::strfmon($data['price']) }}
<b>Betaalmethode:</b> {{ $data['payment_method'] }}
<b>Voertuig:</b> {{ $data['vehicle_name'] }}
<b>Passagiers:</b> {{ $data['people'] }}
@if($data['handluggage'])<b>Handbagage:</b> {{ $data['handluggage'] }} @endif
@if($data['luggage'])<b>Koffers:</b> {{ $data['luggage'] }} @endif
@if(!empty($data['comment']))<b>Opmerkingen:</b> {{ $data['comment'] }} @endif
@if(!empty($data['flightnr']))<b>Vluchtnr:</b> {{ $data['flightnr'] }} @endif

@if (!empty($data['additions']))
-
<b>Extra diensten:</b>
    @foreach ($data['additions'] as $addition)
    {{ $addition['name'] }}
    @endforeach
@endif
-
<b>Klant:</b>
    <b>Naam:</b> {{ $data['user']['name'] }}
    <b>Telefoon:</b> {{ $data['user']['phone']}}
    <b>Email:</b> {{ $data['user']['email']}}
