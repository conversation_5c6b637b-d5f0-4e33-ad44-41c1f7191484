<x-layouts.app>

    <div class="overflow-hidden border rounded-lg">

        <div class="px-4 py-5 border-b border-gray-200 bg-zinc-50 sm:px-6">
            <h1 class="text-xl font-bold leading-5 text-gray-900">Reservering <b>#{{ $rand_id }}</b></h1>
        </div>

        <div class="p-4 md:pt-6">
            <div class="pt-4 pl-0 mt-4 border-t border-gray-300 md:border-t-0 md:pl-4 md:pt-0 md:mt-0">
                <div class="bg-gray-100">
                    <div class="p-6 bg-white md:mx-auto">

                        @if($status === 'success')
                            <svg viewBox="0 0 24 24" class="w-16 h-16 mx-auto my-6 text-green-600">
                            <path fill="currentColor"
                                d="M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z">
                            </path>
                        </svg>

                        @elseif($status === 'pending')
                            <svg viewBox="0 0 24 24" class="w-16 h-16 mx-auto my-6 text-yellow-500">
                                <path fill="currentColor" d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm0,18a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z"/>
                                <path fill="currentColor" d="M12,6a1,1,0,0,0-1,1v5a1,1,0,0,0,.5.87l4,2.5a1,1,0,0,0,1.37-.37,1,1,0,0,0-.37-1.37l-3.5-2.18V7A1,1,0,0,0,12,6Z"/>
                            </svg>

                        @elseif($status === 'failed')
                            <svg viewBox="0 0 24 24" class="w-16 h-16 mx-auto my-6 text-red-600">
                                <path fill="currentColor" d="M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm4.707,15.293a1,1,0,1,1-1.414,1.414L12,13.414,8.707,16.707a1,1,0,0,1-1.414-1.414L10.586,12,7.293,8.707A1,1,0,1,1,8.707,7.293L12,10.586l3.293-3.293a1,1,0,1,1,1.414,1.414L13.414,12Z"/>
                            </svg>

                        @endif
                        <div class="text-center">
                            <h3 class="text-base font-semibold text-center text-gray-900 md:text-2xl">
                                {{ $title }}
                            </h3>
                            <p class="my-2 text-gray-800">{!! $message !!}</p>
                            <div class="py-10">
                                <flux:button href="{{route('home')}}" variant="primary">Terug naar home</flux:button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</x-layouts.app>
