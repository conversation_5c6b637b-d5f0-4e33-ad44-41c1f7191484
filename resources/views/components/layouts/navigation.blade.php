<flux:header container class="py-3 border-b bg-zinc-50 dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700">

    <flux:brand href="{{route('home')}}" logo="{{ asset(@tenant('settings')['logo']) }}" name="{{ @tenant('settings')['sitename'] }}" class="text-blue-500 dark:hidden" />

    <flux:spacer />

    <flux:dropdown>
        <flux:button icon-trailing="chevron-down" class="cursor-pointer">{{ auth()->user()->name ?? 'Inloggen' }}</flux:button>

        <flux:menu>
            <flux:navmenu.item icon="plus" href="{{route('home')}}">Nieuwe Reservering</flux:navmenu.item>
            <flux:menu.separator />

            @guest
                <flux:navmenu.item href="{{route('login')}}">Inloggen</flux:navmenu.item>
                <flux:navmenu.item href="{{route('register')}}">Registreren</flux:navmenu.item>
            @endguest

            @auth
                <flux:navmenu.item href="{{ route('dashboard') }}">{{ __('Dashboard') }}</flux:navmenu.item>
                <flux:navmenu.item href="{{ route('profile') }}">{{ __('Profiel') }}</flux:navmenu.item>
                <flux:navmenu.item href="{{ route('reservations') }}">{{ __('Reserveringen') }}</flux:navmenu.item>
                <flux:navmenu.item href="{{ route('addresses') }}">{{ __('Adressen') }}</flux:navmenu.item>
                <flux:navmenu.item icon="arrow-right-start-on-rectangle" href="{{ route('logout') }}">Uitloggen</flux:navmenu.item>
            @endauth

        </flux:menu>
    </flux:dropdown>
</flux:header>