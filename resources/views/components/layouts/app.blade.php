<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600&display=swap" rel="stylesheet" />

        <title>{{ config('app.name', 'ReserForm') }}</title>
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        @livewireStyles
        @yield('styles')

        @if(isset(tenant('settings')['accent']) && !empty($accent = tenant('settings')['accent']))
        <style>
            :root {
                --color-accent: var(--color-{{$accent}}-500);
                --color-accent-content: var(--color-{{$accent}}-600);
                --color-accent-foreground: var(--color-white);
            }
        </style>
        @endif

    </head>
    <body {{ $attributes
        ->merge(($livewire ?? null)?->getExtraBodyAttributes() ?? [], escape: false)
        ->class([
            'fi-body',
            'fi-panel-' . filament()->getId(),
            'min-h-screen font-normal text-gray-950 antialiased dark:bg-gray-950 dark:text-white',
        ]) }}
    >

        <x-layouts.navigation />

        <div class="px-4 mx-auto bg-white max-w-7xl md:px-6 lg:px-8">
            <!-- Header -->
            @if (isset($header))
                <header class="bg-white shadow-sm">
                    <div class="px-4 py-6 md:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            {{-- Let the customer know he has to set some settings --}}
            @if (
                empty( @tenant('googlemaps')['key'] )/* ||
                empty( tenant('mail')['from_address'] ) ||
                empty( tenant('mail')['from_name'] ) ||
                empty( tenant('mail')['port'] ) ||
                empty( tenant('mail')['host'] ) ||
                empty( tenant('mail')['username'] ) ||
                empty( tenant('mail')['password'] ) */
            )
                <x-alert color="red" message="Er missen enkele vereiste instellingen!" />
            @endif

            <!-- Page Content -->
            <main class="py-4 md:py-6 lg:py-8">
                {{ $slot }}
            </main>

            <!-- Footer -->
            <x-layouts.footer />
        </div>

        <script>window.GOOGLE_MAPS_API_KEY = "{{ @tenant('googlemaps')['key'] }}";</script>

        <script src="https://code.jquery.com/jquery-3.6.4.slim.min.js" integrity="sha256-a2yjHM4jnF9f54xUQakjZGaqYs/V1CYvWpoqZzC2/Bw=" crossorigin="anonymous"></script>
        @livewireScripts
        @fluxScripts
        @yield('scripts')

    </body>
</html>
