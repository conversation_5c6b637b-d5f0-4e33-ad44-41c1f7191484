<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailwind + Google Places Autocomplete (Corrected Loader)</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- 1. Google Maps Bootstrap Loader is placed first in the head -->
    <script>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
        ({key: "AIzaSyDM8V6QQhHWLfmAY_D6vzp_E70glb9Fax8", v: "weekly"});</script>

    <!-- 2. Alpine.js scripts are placed after the Google loader -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        .transition-enter-active, .transition-leave-active { transition: opacity 0.1s ease, transform 0.1s ease; }
        .transition-enter-from, .transition-leave-to { opacity: 0; transform: scale(0.95); }
        .transition-enter-to, .transition-leave-from { opacity: 1; transform: scale(1); }
    </style>
</head>

<body class="bg-gray-100">

    <div class="container max-w-2xl min-h-screen p-4 mx-auto md:p-8">
        <div
            x-data="googlePlacesAutocomplete()"
            x-init="init()"
            class="w-full max-w-sm mx-auto"
        >
            <h1 class="mb-4 text-2xl font-bold text-center text-gray-800">Address Search</h1>

            <div x-on:click.outside="closeDropdown()">
              <label for="combobox" class="block font-medium text-gray-900 text-sm/6">Search for a place</label>
              <div class="relative mt-2">
                <input
                    id="combobox"
                    type="text"
                    class="block w-full rounded-md bg-white py-1.5 pr-12 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                    role="combobox"
                    :aria-expanded="isOpen.toString()"
                    x-model="query"
                    x-on:input="searchPlaces()"
                    x-on:keydown.down.prevent="highlightNext()"
                    x-on:keydown.up.prevent="highlightPrevious()"
                    x-on:keydown.enter.prevent="selectHighlighted();"
                    x-on:keydown.escape.window="closeDropdown()"
                    x-on:focus="isOpen = true"
                    :disabled="!isApiReady"
                    placeholder="Straat + huisnummer ..."
                    autocomplete="off"
                    x-ref="inputElement"
                >

                <ul x-show="isOpen && suggestions.length > 0" x-transition class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black/5 focus:outline-hidden sm:text-sm" id="options" role="listbox" x-ref="listbox">
                  <template x-for="(suggestion, index) in suggestions" :key="suggestion.id + '-' + index">
                      <li
                        {{-- x-on:click="selectHighlighted()" --}}
                        x-on:click="selectSuggestion(suggestion)"
                        x-on:mouseenter="highlightedIndex = index"
                        :class="{ 'text-white bg-indigo-600': highlightedIndex === index, 'text-gray-900': highlightedIndex !== index }"
                        class="relative py-2 pl-3 cursor-default select-none group pr-9"
                        role="option"
                      >

                          {{-- selected checkmark --}}
                        <span class="block truncate" :class="{ 'font-semibold': (selectedPlace &&selectedPlace.id == suggestion.id) }">
                            <span x-text="suggestion.text"></span>
                            <span class="text-gray-400 group-hover:text-indigo-200" x-text="suggestion.text"></span>
                        </span>
                        <span x-show="(selectedPlace && selectedPlace.id == suggestion.id)" class="absolute inset-y-0 right-0 flex items-center pr-4" :class="{ 'text-white': highlightedIndex === index, 'text-indigo-600': highlightedIndex !== index }">
                          <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z" clip-rule="evenodd" /></svg>
                        </span>

                      </li>
                  </template>
                </ul>

              </div>
            </div>

        </div>
    </div>

<script>
function googlePlacesAutocomplete() {
    return {
        query: 'wenenstraat 21',
        suggestions: [],
        selectedPlace: null,
        isOpen: false,
        highlightedIndex: -1,
        places: null,
        isApiReady: false,

        async init() {
            try {
                // This now runs safely because the bootstrap loader has already created the `google` object.
                const { Place, AutocompleteSuggestion } = await google.maps.importLibrary("places");
                this.places = { Place, AutocompleteSuggestion };
                this.isApiReady = true;
            } catch (error) {
                console.error("Failed to load Google Places library", error);
            }
        },

        async searchPlaces() {
            if (this.query.length < 2 || !this.isApiReady) {
                this.suggestions = [];
                return;
            }
            const request = {
                input: this.query,
                locationRestriction: {
                west: -5.15,
                north: 55.10,
                east: 15.50,
                south: 42.30,
                },
                origin: { lat: 52.3105, lng: 4.7683 }, // Schiphol Airport
                language: "nl-NL",
                region: "eu",
            };

            try {
                const { suggestions:rawSuggestions } = await this.places.AutocompleteSuggestion.fetchAutocompleteSuggestions(request);

                // 3. THE KEY STEP: Transform the raw data into a  simple array
                if (rawSuggestions) {
                    this.suggestions = rawSuggestions.map(suggestion => {
                        return {

                            text: suggestion.placePrediction.text.text,
                            id: suggestion.placePrediction.placeId
                        };
                    });
                }

            } catch (error) {
                this.suggestions = [];
            }
        },

        async selectSuggestion(suggestion) {

            if (!this.isApiReady) return;

            this.query = suggestion.text;
            this.selectedPlace = suggestion;

            this.closeDropdown();
        },

        closeDropdown() { this.isOpen = false; this.highlightedIndex = -1; this.$refs.inputElement.blur(); },

        toggleDropdown() { this.isOpen = !this.isOpen; },

        // reset() { this.query = ''; this.selectedPlace = null; this.suggestions = []; this.closeDropdown(); },
        highlightNext() { if (this.suggestions.length > 0) this.highlightedIndex = (this.highlightedIndex + 1) % this.suggestions.length; },
        highlightPrevious() { if (this.suggestions.length > 0) this.highlightedIndex = (this.highlightedIndex - 1 + this.suggestions.length) % this.suggestions.length; },
        selectHighlighted() {
            if (this.highlightedIndex > -1){
                this.selectSuggestion(this.suggestions[this.highlightedIndex]);
            }
        }
    }
}
</script>

</body>
</html>
