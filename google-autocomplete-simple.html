<!DOCTYPE html>
<html>
  <head>
    <title>Place Autocomplete Data API Predictions</title>
    <style>
      #title {
        background-color: #fff;
        border: 2px solid #fff;
        border-radius: 3px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        box-sizing: border-box;
        font-family: <PERSON>o;
        font-size: 15px;
        font-weight: 300;
        height: 29px;
        margin-left: 17px;
        margin-top: 10px;
        outline: none;
        padding: 0 11px 0 13px;
        text-overflow: ellipsis;
        width: 400px;
      }

      #title:focus {
        border-color: #4d90fe;
      }

      #prediction {
        font-family: Roboto;
        font-size: 15px;
        font-weight: 300;
        margin-left: 17px;
        margin-top: 10px;
      }

      .powered-by-google {
        margin-left: 17px;
        margin-top: 10px;
      }
    </style>
  </head>
  <body>
    <div id="title">
      <input type="text" id="search-input" placeholder="Enter a place" />
    </div>
    <ul id="results"></ul>
    <p><span id="prediction"></span></p>
    <img
      class="powered-by-google"
      src="https://storage.googleapis.com/geo-devrel-public-buckets/powered_by_google_on_white.png"
      alt="Powered by Google"
    />

    <!-- prettier-ignore -->
    <script>(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})
        ({key: "AIzaSyDM8V6QQhHWLfmAY_D6vzp_E70glb9Fax8", v: "weekly"});</script>

    <script>
      let autocompleteService;
      let placesService;
      let searchInput;
      let resultsList;
      let prediction;

      async function init() {
        const { AutocompleteService, PlacesService } = await google.maps.importLibrary("places");
        
        autocompleteService = new AutocompleteService();
        placesService = new PlacesService(document.createElement("div")); // Dummy div
        searchInput = document.getElementById("search-input");
        resultsList = document.getElementById("results");
        prediction = document.getElementById("prediction");

        searchInput.addEventListener("input", () => {
          const request = { input: searchInput.value };
          autocompleteService.getPlacePredictions(request, displaySuggestions);
        });
      }

      function displaySuggestions(predictions, status) {
        resultsList.innerHTML = "";

        if (status != google.maps.places.PlacesServiceStatus.OK || !predictions) {
          return;
        }

        predictions.forEach((p) => {
          const li = document.createElement("li");
          li.innerText = p.description;
          li.addEventListener("click", () => {
            const request = {
              placeId: p.place_id,
              fields: ["name", "formatted_address", "place_id", "geometry"],
            };
            placesService.getDetails(request, (place, status) => {
              if (status === google.maps.places.PlacesServiceStatus.OK) {
                prediction.innerText = JSON.stringify(place, null, 2);
                resultsList.innerHTML = "";
              }
            });
          });
          resultsList.appendChild(li);
        });
      }

      init();
    </script>
  </body>
</html>
